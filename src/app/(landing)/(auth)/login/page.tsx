'use client';

import React from 'react';
import { usePrefetchRoutes } from '@shared/hooks/usePrefetchRoutes';
import useLocation from '@shared/utils/hooks/useLocation';
import Button from 'shared/uikit/Button';
import DividerTitled from 'shared/uikit/Divider/DividerTitled';
import LoginForm from 'shared/uikit/LoginForm';
import cnj from 'shared/uikit/utils/cnj';
import {
  landingRouteNames,
  mainRoutes,
} from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useOnSuccessLogin from 'shared/utils/hooks/useOnSuccessLogin';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AuthFormLayout from '../../shared/components/layouts/AuthFormLayout';
import classes from './page.module.scss';

const Login = (): JSX.Element => {
  const { t } = useTranslation();
  const onSuccess = useOnSuccessLogin();
  const history = useHistory();
  usePrefetchRoutes([mainRoutes.home]);

  const handleNotVerify = () => {
    history.push(landingRouteNames.getCode);
  };
  const { preserveSearchParams } = useLocation();

  return (
    <AuthFormLayout title={t('login')}>
      <LoginForm onSuccess={onSuccess} onNotVerify={handleNotVerify} />
      <DividerTitled
        label={t('y_h_an_acc')}
        className={cnj(classes.dividerTitled)}
      />
      <Button
        fullWidth
        variant="xLarge"
        schema="semi-transparent"
        className={classes.borderRadius}
        label={t('signup')}
        to={preserveSearchParams(landingRouteNames.signup)}
      />
    </AuthFormLayout>
  );
};

export default Login;
