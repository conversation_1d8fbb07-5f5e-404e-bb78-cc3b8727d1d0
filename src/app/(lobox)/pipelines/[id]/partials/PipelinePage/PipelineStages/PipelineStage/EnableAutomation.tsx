import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Switch from '@shared/uikit/Switch';
import Typography from '@shared/uikit/Typography';
import { postPipelineAutomationEnabled } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { PipelineInfo } from '@shared/types/pipelineProps';

type EnableAutomationProps = {
  stage: PipelineInfo;
};

export function EnableAutomation({ stage }: EnableAutomationProps) {
  const { t } = useTranslation();
  const [enable, setEnable] = useState(stage?.automationEnabled);
  const queryClient = useQueryClient();
  const stageId = stage?.id;

  const { mutate: postEnableAutomation } = useReactMutation({
    apiFunc: postPipelineAutomationEnabled,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getPipeline, stageId],
      });
    },
    onError: () => {
      setEnable(false);
    },
  });

  const onChangeSwitch = () => {
    postEnableAutomation({
      id: stageId,
      enabled: !enable,
    });
    setEnable((prev) => !prev);
  };

  return (
    <Flex className="sticky bottom-0 left-0 w-full bg-popOverBg_white px-20 pb-20 pt-12 flex flex-col gap-8 border-t border-t-techGray_20 border-solid">
      <Flex flexDir="row" className="justify-between items-center">
        <Flex flexDir="row" className="gap-0.5">
          <Typography className="!text-xs !font-bold !text-smoke_coal ">
            {stage.automationsCount}
          </Typography>
          <Typography className="!text-xs !text-secondaryDisabledText">
            {t('automated_action')}
          </Typography>
        </Flex>

        <Flex flexDir="row" className="gap-8 items-center">
          <Typography className="!text-smoke_coal text-base font-medium">
            {t('automation')}
          </Typography>

          <div>
            <Switch value={enable} onChange={onChangeSwitch} />
          </div>
        </Flex>
      </Flex>
      <Button
        label={t('automate_stage')}
        leftIcon="automate"
        leftType="far"
        schema="semi-transparent"
        onClick={() =>
          openMultiStepForm({
            formName: 'automation',
            data: stage,
            type: 'main',
          })
        }
      />
    </Flex>
  );
}
