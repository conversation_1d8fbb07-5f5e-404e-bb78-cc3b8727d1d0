'use client';
import Flex from '@shared/uikit/Flex';
import { CandidateCardSkeleton } from '@shared/components/molecules/CandidateCard';
import Skeleton from '@shared/uikit/Skeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Grid from '@shared/uikit/Grid';

export default function loading() {
  return (
    <Flex className="m-20 gap-20 h-full">
      <CandidateCardSkeleton
        footer={
          <Flex className="!flex-row gap-12 py-[15px] mx-5">
            {Array.from({ length: 8 }).map((_, index) => (
              <Skeleton key={index} className="w-32 flex-1 h-32 rounded" />
            ))}
          </Flex>
        }
      />
      <CardWrapper classNames={{ root: 'flex-1 rounded' }}>
        <Flex flexDir="row" className="justify-between">
          <Skeleton className="!w-[250px] h-[30px] rounded-lg" />
          <Skeleton className="!w-[350px] h-[30px] rounded-lg" />
        </Flex>
        <Grid container spacing={1.5}>
          {Array.from({ length: 9 }).map((_, index) => (
            <Grid size={4} key={index}>
              <Skeleton className="w-[100px] h-[200px] rounded-lg" />
            </Grid>
          ))}
        </Grid>
      </CardWrapper>
    </Flex>
  );
}
