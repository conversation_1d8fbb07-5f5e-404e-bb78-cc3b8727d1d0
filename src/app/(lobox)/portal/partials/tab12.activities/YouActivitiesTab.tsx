import { searchCandidateActivities } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import Section from '@shared/components/molecules/Section';
import SearchList from '@shared/components/Organism/SearchList';
import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';

export default function YouActivitiesTab() {
  const { t } = useTranslation();

  const {
    content: list,
    isLoading,
    totalPages,
    setPage,
  } = usePaginateQuery({
    action: {
      apiFunc: searchCandidateActivities,
      key: [QueryKeys.candidateActivities],
    },
  });

  return (
    <Section className="h-auto flex-1 bg-popOverBg px-20 rounded-lg">
      <SearchList
        ItemSkeleton={ActivityItemSkeleton}
        title={t('activities')}
        hasSubTitle={false}
        data={list}
        isLoading={isLoading}
        renderItem={(item, index) => (
          <ActivityItem key={`row_item_${index}`} item={item} />
        )}
        emptyList={
          <EmptySectionInModules
            title={t('no_activities')}
            classNames={{ container: 'h-full' }}
          />
        }
        className={{
          wrapper: '!p-0 !m-0 !my-20',
        }}
        totalPages={totalPages}
        onPageChange={(page) => {
          setPage(page);
        }}
        scrollToTopWhenClick={false}
      />
    </Section>
  );
}
