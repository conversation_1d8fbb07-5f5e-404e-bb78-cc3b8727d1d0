import { JobReviewProps } from '@shared/types/review';
import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import ReviewItem from '@shared/components/molecules/ReviewItem';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import { useParams } from 'next/navigation';

export default function YouReviewsTab() {
  const { t } = useTranslation();
  const params = useParams();

  return (
    <YouTabContentContainer<JobReviewProps>
      sectionTitle={t('reviews')}
      entity="recruiterReviews"
      extraParams={{ teamMemberId: params?.teamMemberId }}
      component={(review) => <ReviewItem item={review} variant="you" />}
      emptySectionMessage={t('no_reviews_found')}
      rootName="reviews"
    />
  );
}
