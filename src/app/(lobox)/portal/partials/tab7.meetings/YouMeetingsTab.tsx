import React from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import { MeetingCard } from '@shared/components/molecules/MeetingCard';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { MeetingDataProps } from '@shared/types/meeting';
import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import { searchFilterQueryParams } from '@shared/constants/search';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import { useParams } from 'next/navigation';

export default function YouMeetingsTab() {
  const { t } = useTranslation();
  const { setScheduleEventsPanelData } = useSchedulesUrlState();
  const dynamicFilters = useDynamicFilters();
  const getQueryValue = useGetNormalizedArrayQuery();
  const { show: showFilters } = useYouFilters();
  const { SORT_BY } = useCommonFilterGroups();
  const params = useParams();
  const CREATED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.creatorUserIds,
    options: dynamicFilters.creators,
    label: t('created_by'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.creatorUserIds, 'array'),
  };

  const groups = [SORT_BY, CREATED_BY].filter(Boolean) as object[];

  return (
    <YouTabContentContainer<MeetingDataProps>
      entity="recruiterMeetings"
      extraParams={{ teamMemberId: params?.teamMemberId }}
      sectionTitle={t('meetings')}
      component={(meeting) => (
        <MeetingCard
          key={`meeting_${meeting.id}`}
          actionProps={{
            label: t('view_meeting'),
            onClick: () =>
              setScheduleEventsPanelData({
                eventId: meeting.id,
                isFromNotification: true,
                isInCandidateManager: true,
                isInCrEdit: false,
                schedulesEventType: ScheduleEventTypes.MEETING,
              }),
          }}
          item={{ meeting: { ...meeting, type: ScheduleEventTypes.MEETING } }}
          cardProps={{
            classNames: {
              container: 'border border-solid border-techGray_20 rounded-xl',
            },
          }}
        />
      )}
      emptySectionMessage={t('no_meetings_found')}
      showFilters={showFilters}
      rootName="meetings"
      groups={groups}
    />
  );
}
