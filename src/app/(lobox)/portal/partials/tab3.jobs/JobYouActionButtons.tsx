import { useRouter } from 'next/navigation';
import * as React from 'react';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import Flex from '@shared/uikit/Flex';
import { mainRoutes, routeNames } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from 'shared/uikit/Button';
import type { SingleJobAPIProps } from '@shared/types/jobsProps';

interface BusinessJobCardActionsProps {
  job: SingleJobAPIProps;
}

export const JobYouActionButtons: React.FunctionComponent<
  BusinessJobCardActionsProps
> = ({ job }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });

  return (
    <Flex className="!flex-row gap-12">
      <Button
        label={t('view_details')}
        schema="semi-transparent"
        fullWidth
        to={`${routeNames.searchRecruiterJobs}?currentEntityId=${job?.id}`}
      />
      {job.status === 'UNPUBLISHED' ? (
        <Button
          label={t('edit')}
          onClick={() => onAction('edit', job)}
          rightIcon="edit"
          fullWidth
        />
      ) : (
        <Button
          label={t('track')}
          rightIcon="chevron-right"
          fullWidth
          to={`${mainRoutes.pipelines}/${job?.id}`}
        />
      )}
    </Flex>
  );
};
