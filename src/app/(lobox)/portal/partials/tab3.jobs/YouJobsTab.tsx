import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import { JobAPIProps } from '@shared/types/jobsProps';
import BusinessJobCardInList from '@shared/components/molecules/BusinessJobCard/BusinessJobCardInList';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import { useRecruiterJobsFilterGroups } from '@shared/hooks/searchFilters/useRecruiterJobsFilterGroups';
import RecruiterJobDetailsCardMoreOptions from 'app/(lobox)/search/my-jobs/partials/RecruiterJobs/RecruiterJobDetails/RecruiterJobDetailsCard/RecruiterJobDetailsCardMoreOptions';
import { JobYouActionButtons } from 'app/(lobox)/portal/partials/tab3.jobs/JobYouActionButtons';
import { useParams } from 'next/navigation';

export default function YouJobsTab() {
  const { t } = useTranslation();
  const { show: showFilters } = useYouFilters();
  const groups = useRecruiterJobsFilterGroups();
  const params = useParams();

  return (
    <YouTabContentContainer<JobAPIProps>
      entity="recruiterJobs"
      extraParams={{ onlyInvolved: true, teamMemberId: params?.teamMemberId }}
      sectionTitle={t('jobs')}
      component={(item) => (
        <BusinessJobCardInList
          job={item}
          moreOptions={<RecruiterJobDetailsCardMoreOptions job={item} />}
          actions={<JobYouActionButtons job={item} />}
          cardProps={{
            classNames: {
              container: 'border border-solid border-techGray_20 rounded-xl',
            },
          }}
          key={item.id}
        />
      )}
      emptySectionMessage={t('no_jobs_found')}
      showFilters={showFilters}
      groups={groups}
      rootName="jobs"
    />
  );
}
