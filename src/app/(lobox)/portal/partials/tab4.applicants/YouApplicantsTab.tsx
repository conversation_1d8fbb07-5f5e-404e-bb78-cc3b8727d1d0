import { JobParticipationModel } from '@shared/types/jobsProps';
import React from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { YouCandidateApplicantCard } from 'app/(lobox)/portal/partials/YouCandidateApplicantCard';
import { useParams } from 'next/navigation';

export default function YouApplicantsTab() {
  const { t } = useTranslation();
  const params = useParams();

  return (
    <YouTabContentContainer<JobParticipationModel>
      entity="searchParticipationAsApplicant"
      extraParams={{ onlyInvolved: true, teamMemberId: params?.teamMemberId }}
      sectionTitle={t('applicants')}
      component={(item) => (
        <YouCandidateApplicantCard item={item} variant="applicant" />
      )}
      emptySectionMessage={t('no_applicants_found')}
      rootName="applicants"
    />
  );
}
