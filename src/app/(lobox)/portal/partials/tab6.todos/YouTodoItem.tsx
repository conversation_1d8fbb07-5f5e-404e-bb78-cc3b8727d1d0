import TodoItem from '@shared/components/molecules/TodoItem';
import TodoStatus from '@shared/components/molecules/TodoItem/partials/TodoStatus';
import { ICandidateTodo } from '@shared/types/candidates';
import { TodoStatusType } from '@shared/types/todo';
import Flex from '@shared/uikit/Flex';

import request from '@shared/utils/toolkit/request';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import React, { FC } from 'react';
import {
  candidateEndpoints,
  jobsEndpoints,
} from '@shared/utils/constants/servicesEndpoints';
import { QueryKeys } from '@shared/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import cnj from '@shared/uikit/utils/cnj';

export interface YouTodoItemProps {
  item: ICandidateTodo;
  className?: string;
}
// TODO: replace with owner entity type and owner entity id
const isCandidateMode = true;

export const YouTodoItem: FC<YouTodoItemProps> = ({ item, className }) => {
  const queryClient = useQueryClient();
  const onSuccessHandler = () => {
    setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.candidateTodos],
      });
    }, 300);
  };

  const { mutate, isPending } = useReactMutation({
    apiFunc: async (params: { status: TodoStatusType; id: string }) => {
      const { data } = await request.put(
        isCandidateMode
          ? candidateEndpoints.setTodoStatus(params)
          : jobsEndpoints.setTodoStatus(params)
      );

      return data;
    },
    onSuccess: onSuccessHandler,
  });

  return (
    <TodoItem
      item={item}
      variant="you"
      displayCreator
      cardWrapperProps={{
        classNames: {
          root: cnj(
            'bg-background border border-solid border-techGray_20 rounded-b-[calc(var(--spacing-xLargeGutter)*0.5)]',
            className
          ),
        },
      }}
      action={
        <Flex flexDir="row" className="justify-between align-center">
          <TodoStatus
            disabled={isPending}
            value={item?.status}
            onChange={(_status) => {
              if (item?.id && _status?.value)
                mutate({ id: item.id, status: _status.value });
            }}
          />
        </Flex>
      }
    />
  );
};
