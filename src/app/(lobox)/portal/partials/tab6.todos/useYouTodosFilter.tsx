import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo } from 'react';

export const useYouTodosFilter = () => {
  const { t } = useTranslation();
  const { DATE_POSTED } = useCommonFilterGroups();

  const CREATED_BY = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('created_by'),
      },
      cp: 'checkBoxGroup',
      label: t('created_by'),
      withConfirmation: false,
      name: 'creatorIds',
      options: [],
      divider: {
        className: 'my-20',
      },
    }),
    [t]
  );

  const ASSIGNEES = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('assignees'),
      },
      cp: 'checkBoxGroup',
      label: t('assignees'),
      withConfirmation: false,
      name: 'assignees',
      options: [],
      divider: {
        className: 'my-20',
      },
    }),
    [t]
  );

  return [DATE_POSTED, CREATED_BY, ASSIGNEES].filter(<PERSON><PERSON>an);
};
