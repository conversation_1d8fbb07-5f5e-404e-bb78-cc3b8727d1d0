import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import { YouTodoItem } from 'app/(lobox)/portal/partials/tab6.todos/YouTodoItem';
import { ICandidateTodo } from '@shared/types/candidates';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import { useYouTodosFilter } from './useYouTodosFilter';
import { useParams } from 'next/navigation';

export default function YouTodosTab() {
  const { t } = useTranslation();
  const groups = useYouTodosFilter();
  const { show: showFilters } = useYouFilters();
  const params = useParams();

  return (
    <YouTabContentContainer<ICandidateTodo>
      sectionTitle={t('todos')}
      entity="recruiterTodos"
      extraParams={{ teamMemberId: params?.teamMemberId }}
      component={(item) => (
        <YouTodoItem
          item={item}
          className="border border-solid border-techGray_20 rounded-xl"
          key={item.id}
        />
      )}
      emptySectionMessage={t('no_todos_found')}
      showFilters={showFilters}
      groups={groups}
      rootName="todos"
    />
  );
}
