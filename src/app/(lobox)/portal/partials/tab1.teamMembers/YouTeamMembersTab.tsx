'use client';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';
import { searchTeamMembers } from '@shared/utils/api/search';
import { QueryKeys } from '@shared/utils/constants';
import { ISearchTeamMembers } from '@shared/types/search';
import YouTabContentContainerWithoutFilters from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithoutFilters';
import TeamMemberCard from '@shared/components/Organism/TeamMemberCard';

export default function YouTeamMembersTab() {
  const { t } = useTranslation();

  return (
    <YouTabContentContainerWithoutFilters<ISearchTeamMembers>
      queryKey={[QueryKeys.searchTeamMembers]}
      apiFunc={searchTeamMembers}
      extraParams={{
        onlyInvolved: true,
      }}
      sectionTitle={t('team_members')}
      component={(item) => <TeamMemberCard item={item} />}
      emptySectionMessage={t('no_team_members_found')}
      rootName="teamMembers"
    />
  );
}
