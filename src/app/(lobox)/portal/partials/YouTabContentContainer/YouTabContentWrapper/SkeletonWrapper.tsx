import React from 'react';
import { SKELETON_NUMBERS } from '@shared/constants/enums';
import { YouTabkeys } from '@shared/types/you';
import TeamMemberSkeleton from '@shared/components/Organism/TeamMemberCard/TeamMemberSkeleton';
import Grid from '@shared/uikit/Grid';
import BaseProjectCardSkeleton from '@shared/components/molecules/ProjectCard/BaseProjectCardSkeleton';
import { CandidateCardSkeleton } from '@shared/components/molecules/CandidateCard';
import { BusinessJobCardSkeleton } from '@shared/components/molecules/BusinessJobCard';
import Skeleton from '@shared/uikit/Skeleton';
import YouTodoItemSkeleton from '@shared/components/molecules/TodoItem/YouTodoItemSkeleton';
import MeetingCardSkeleton from '@shared/components/molecules/MeetingCard/MeetingCardSkeleton';
import NoteItemSkeleton from '@shared/components/molecules/NoteItem/NoteItemSkeleton';
import ReviewItemSkeleton from '@shared/components/molecules/ReviewItem/ReviewItemSkeleton';
import YouReviewItemSkeleton from '@shared/components/molecules/ReviewItem/YouReviewItemSkeleton';
import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';

export interface SkeletonWrapperProps {
  className?: string;
  totalElements?: number;
  rootName: YouTabkeys;
}

const SkeletonWrapper: React.FC<SkeletonWrapperProps> = ({
  className,
  totalElements = 3,
  rootName,
}) => {
  const list = Array(totalElements)
    .fill(0)
    .map((_, i) => i);

  return (
    <Grid container spacing={1.5} mb={1.5}>
      {list.map((i) => (
        <Grid key={`${i}`} size={4}>
          {LoadingSkeletons({ rootName })}
        </Grid>
      ))}
    </Grid>
  );
};

const LoadingSkeletons = ({ rootName }: { rootName: YouTabkeys }) => {
  switch (rootName) {
    case 'teamMembers':
      return <TeamMemberSkeleton />;
    case 'projects':
      return (
        <BaseProjectCardSkeleton
          className="border border-solid !border-gray_5"
          variant="list"
        />
      );
    case 'jobs':
      return (
        <BusinessJobCardSkeleton className="border border-solid !border-gray_5" />
      );
    case 'applicants':
      return (
        <CandidateCardSkeleton
          showTags
          className="border border-solid !border-gray_5"
        >
          <Skeleton className="w-full h-[32px] rounded" />
        </CandidateCardSkeleton>
      );

    case 'candidates':
      return (
        <CandidateCardSkeleton
          showTags
          className="border border-solid !border-gray_5"
        >
          <Skeleton className="w-full h-[32px] rounded" />
        </CandidateCardSkeleton>
      );

    case 'todos':
      return <YouTodoItemSkeleton />;

    case 'meetings':
      return <MeetingCardSkeleton />;

    case 'notes':
      return (
        <NoteItemSkeleton
          classNames={{
            container: 'border border-solid !border-gray_5 rounded-xl',
          }}
        />
      );

    case 'reviews':
      return <YouReviewItemSkeleton />;

    // case 'activities':
    //   return <ActivityItemSkeleton />;

    default:
      break;
  }
};

export default SkeletonWrapper;
