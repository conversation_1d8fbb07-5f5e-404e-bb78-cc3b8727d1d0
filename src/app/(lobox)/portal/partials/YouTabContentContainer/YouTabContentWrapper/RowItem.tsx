import { ItemComponent } from './ItemComponent';
import Flex from '@shared/uikit/Flex';

export const RowItems = <T,>({
  index,
  component,
  _data,
}: {
  index: number;
  component: (item: T) => React.ReactNode;
  _data: T[][];
}) => {
  const item1 = _data?.[index]?.[0];
  const item2 = _data?.[index]?.[1];
  const item3 = _data?.[index]?.[2];

  return (
    <Flex className="flex flex-col flex-wrap md:flex-row md:gap-12">
      {!!item1 && <ItemComponent item={item1} component={component} />}
      {!!item2 && <ItemComponent item={item2} component={component} />}
      {!!item3 && <ItemComponent item={item3} component={component} />}
    </Flex>
  );
};
