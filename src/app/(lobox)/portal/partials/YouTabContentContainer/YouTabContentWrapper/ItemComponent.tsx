import BaseButton from '@shared/uikit/Button/BaseButton';
import React from 'react';

export const ItemComponent = <T,>({
  item,
  component,
}: {
  item: T;
  component: (item: T) => React.ReactNode;
}) => {
  return (
    <BaseButton
      className={
        'w-full overflow-hidden pb-4 last:mr-0 last:pl-0 md:w-[calc((100%-24px)/3)] md:pb-12'
      }
    >
      {component(item)}
    </BaseButton>
  );
};
