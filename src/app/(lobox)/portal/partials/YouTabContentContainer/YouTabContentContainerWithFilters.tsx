import { ObjectSectionContainerProps } from '@shared/components/molecules/Section';
import React, { useEffect } from 'react';
import useSearchResultWithFilters, {
  ISearchEntity,
} from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import YouTabContentWrapper from './YouTabContentWrapper';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { YouTabkeys } from '@shared/types/you';

interface YouTabContentContainerProps<T> {
  component: (item: T) => React.ReactNode;
  emptySectionMessage: string;
  showFilters?: () => void;
  sectionTitle: string;
  sectionProps?: ObjectSectionContainerProps;
  groups?: object[];
  rootName?: YouTabkeys;
  entity: ISearchEntity;
  extraParams?: object;
}

export default function YouTabContentContainer<T>({
  component,
  emptySectionMessage,
  showFilters,
  sectionTitle,
  sectionProps,
  groups,
  rootName,
  entity,
  extraParams,
}: YouTabContentContainerProps<T>) {
  const { setValue, debounceValue } = useDebounceState('', 500);
  const { handleChangeParams } = useCustomParams();

  const {
    content: list,
    isLoading,
    totalPages,
    setPage,
  } = useSearchResultWithFilters({
    entity,
    extraParams: { ...extraParams },
    options: { shouldScrollToTop: false },
  });

  useEffect(() => {
    handleChangeParams({ add: { query: debounceValue || '' } });
  }, [debounceValue]);

  return (
    <YouTabContentWrapper<T>
      list={list}
      isLoading={isLoading}
      totalPages={totalPages}
      setPage={setPage}
      component={component}
      emptySectionMessage={emptySectionMessage}
      onSearchChanged={setValue}
      showFilters={showFilters}
      sectionTitle={sectionTitle}
      sectionProps={sectionProps}
      groups={groups}
      rootName={rootName}
    />
  );
}
