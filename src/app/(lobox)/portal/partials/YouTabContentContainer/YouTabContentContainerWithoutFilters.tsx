import { ObjectSectionContainerProps } from '@shared/components/molecules/Section';
import React from 'react';
import YouTabContentWrapper from './YouTabContentWrapper';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { PaginateResponse } from '@shared/types/response';
import { QueryKey } from '@tanstack/react-query';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import { YouTabkeys } from '@shared/types/you';

interface YouTabContentContainerProps<T> {
  component: (item: T) => React.ReactNode;
  emptySectionMessage: string;
  sectionTitle: string;
  sectionProps?: ObjectSectionContainerProps;
  extraParams?: object;
  apiFunc: (params: any) => Promise<PaginateResponse<T>>;
  queryKey: QueryKey;
  rootName?: YouTabkeys;
}

export default function YouTabContentContainerWithoutFilters<T>({
  component,
  emptySectionMessage,
  sectionTitle,
  sectionProps,
  apiFunc,
  queryKey,
  extraParams,
  rootName,
}: YouTabContentContainerProps<T>) {
  const { setValue: setSearchValue, debounceValue } = useDebounceState('', 500);

  const {
    content: list,
    isLoading,
    totalPages,
    setPage,
  } = usePaginateQuery({
    action: {
      apiFunc,
      key: [...queryKey, debounceValue],
      params: {
        ...extraParams,
        text: debounceValue,
      },
    },
  });

  return (
    <YouTabContentWrapper<T>
      list={list}
      isLoading={isLoading}
      totalPages={totalPages}
      setPage={setPage}
      component={component}
      emptySectionMessage={emptySectionMessage}
      onSearchChanged={setSearchValue}
      sectionTitle={sectionTitle}
      sectionProps={sectionProps}
      rootName={rootName}
    />
  );
}
