import { createContext, useContext } from 'react';
import type { Dispatch, SetStateAction } from 'react';
import type { YouTabkeys } from '@shared/types/you';

export interface YouContextValue {
  selectedTab: YouTabkeys;
  setSelectedTab: Dispatch<SetStateAction<YouTabkeys>>;
}

const YouContext = createContext<YouContextValue>({
  selectedTab: 'teamMembers',
  setSelectedTab: () => {},
} as YouContextValue);

export default YouContext.Provider;

export function useYouContext() {
  return useContext(YouContext);
}
