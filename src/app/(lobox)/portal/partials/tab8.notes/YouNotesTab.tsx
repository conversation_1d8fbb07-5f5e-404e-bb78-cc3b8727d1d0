import { ICandidateNote } from '@shared/types/candidates';
import { searchCandidateNotes } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import React from 'react';
import YouTabContentContainerWithoutFilters from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithoutFilters';
import useTranslation from '@shared/utils/hooks/useTranslation';
import NoteItem from '@shared/components/molecules/NoteItem';
import { useParams } from 'next/navigation';

export default function YouNotesTab() {
  const { t } = useTranslation();
  const params = useParams();

  return (
    <YouTabContentContainerWithoutFilters<ICandidateNote>
      sectionTitle={t('notes')}
      apiFunc={searchCandidateNotes}
      queryKey={[QueryKeys.candidateNotes]}
      extraParams={{ teamMemberId: params?.teamMemberId }}
      component={(note: ICandidateNote) => <NoteItem item={note} />}
      emptySectionMessage={t('no_notes_found')}
      rootName="notes"
    />
  );
}
