import ReviewButton from '@shared/components/molecules/ReviewButton';
import useChangePipeline from '@shared/hooks/useChangePipeline';
import { JobProps } from '@shared/types/jobsProps';
import { CandidateFormData } from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';
import { mainRoutes } from '@shared/utils/constants/routeNames';
import { useRouter } from 'next/navigation';

const YouCandidateApplicantCardActionButtons = ({
  user,
  job,
  changePiplineVariant = 'candidate',
}: {
  user: CandidateFormData;
  job: JobProps;
  changePiplineVariant?: 'applicant' | 'candidate';
}) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { onChangePipeline } = useChangePipeline({
    onSuccess: () => {},
    variant: changePiplineVariant,
  });

  const onRedirectToPipeline = () => {
    router.push(`${mainRoutes.pipelines}/${job?.id}`);
  };

  return (
    <Flex className="!flex-row gap-12">
      <ReviewButton
        pipelines={job?.pipelines}
        onChangePipeline={(pipelineId) =>
          onChangePipeline({
            userId: user.id,
            pipelineId: pipelineId as string,
          })
        }
        stageTitle={job?.pipeline?.title || ''}
      />
      <Button
        className="w-full"
        label={t('track')}
        leftIcon="user-cog"
        fullWidth
        onClick={onRedirectToPipeline}
      />
    </Flex>
  );
};

export default YouCandidateApplicantCardActionButtons;
