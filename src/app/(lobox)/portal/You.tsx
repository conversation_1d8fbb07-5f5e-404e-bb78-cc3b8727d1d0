'use client';

import Tabs, { TabType } from 'shared/components/Organism/Tabs';
import React, { useState } from 'react';
import type { PortalPanelsTabkeys } from '@shared/types/portal';
import useTranslation from '@shared/utils/hooks/useTranslation';
import CandidateCard, {
  CandidateCardSkeleton,
} from '@shared/components/molecules/CandidateCard';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import Flex from '@shared/uikit/Flex';
import YouContext from 'app/(lobox)/portal/partials/YouContext';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { UserType } from '@shared/types/user';
import Skeleton from '@shared/uikit/Skeleton';
import Divider from '@shared/uikit/Divider';
import dynamic from 'next/dynamic';
import SkeletonWrapper from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentWrapper/SkeletonWrapper';
import { YouTabkeys } from '@shared/types/you';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';

const LoadingSkeleton = ({ tab }: { tab: YouTabkeys }) => {
  return (
    <Skeleton className="w-full rounded-xl px-20 !bg-popOverBg">
      <CardWrapper
        classNames={{
          container:
            '!flex !flex-row !justify-between !items-center !bg-popOverBg !px-0',
        }}
      >
        <Flex className="gap-4">
          <Skeleton className="!w-[130px] !h-[16px] rounded" />
          <Skeleton className="!w-[70px] !h-[16px] rounded" />
        </Flex>
        <Skeleton className="!w-[120px] !h-[32px] rounded" />
      </CardWrapper>
      <SkeletonWrapper rootName={tab} totalElements={12} />
    </Skeleton>
  );
};

const YouTeamMembersTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab1.teamMembers'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'teamMembers' }),
  }
);

const YouProjectsTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab2.projects'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'projects' }),
  }
);

const YouJobsTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab3.jobs'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'jobs' }),
  }
);

const YouApplicantsTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab4.applicants'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'applicants' }),
  }
);

const YouCandidatesTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab5.candidates'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'candidates' }),
  }
);

const YouTodosTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab6.todos'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'todos' }),
  }
);

const YouMeetingsTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab7.meetings'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'meetings' }),
  }
);

const YouNotesTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab8.notes'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'notes' }),
  }
);

const YouReviewsTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab10.reviews'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'reviews' }),
  }
);

const YouActivitiesTab = dynamic(
  () => import('app/(lobox)/portal/partials/tab12.activities'),
  {
    ssr: false,
    loading: () => LoadingSkeleton({ tab: 'activities' }),
  }
);

export function YouContainer({
  isUserPortal = false,
  user,
  isLoading = false,
}: {
  isUserPortal?: boolean;
  user: UserType;
  isLoading?: boolean;
}) {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<PortalPanelsTabkeys>(
    isUserPortal ? 'projects' : 'teamMembers'
  );

  const { resetFilters } = useYouFilters();

  const onChangeTab = (tab: PortalPanelsTabkeys) => {
    setSelectedTab(tab);
    resetFilters();
  };
  const tabs: TabType<PortalPanelsTabkeys>[] = [
    !isUserPortal && {
      path: 'teamMembers',
      title: t('team_members'),
    },
    {
      path: 'projects',
      title: t('projects'),
    },
    {
      path: 'jobs',
      title: t('jobs'),
    },
    {
      path: 'applicants',
      title: t('applicants'),
    },
    {
      path: 'candidates',
      title: t('candidates'),
    },
    {
      path: 'todos',
      title: t('todos'),
    },
    {
      path: 'meetings',
      title: t('meetings'),
    },
    {
      path: 'notes',
      title: t('notes'),
    },
    {
      path: 'threads',
      title: t('threads'),
    },
    {
      path: 'reviews',
      title: t('reviews'),
    },
    {
      path: 'assessments',
      title: t('assessments'),
    },
    {
      path: 'activities',
      title: t('activities'),
    },
    {
      path: 'insights',
      title: t('insights'),
    },
  ].filter(Boolean) as TabType<PortalPanelsTabkeys>[];

  return (
    <Flex className="m-20 gap-20 h-full overflow-y-auto">
      <YouContext
        value={{
          selectedTab,
          setSelectedTab,
        }}
      >
        {isLoading ? (
          <CandidateCardSkeleton
            footer={
              <Flex className="!flex-row gap-12 py-[15px] mx-5">
                {Array.from({ length: 8 }).map((_, index) => (
                  <Skeleton key={index} className="w-32 flex-1 h-32 rounded" />
                ))}
              </Flex>
            }
          />
        ) : (
          <CandidateCard
            avatar={user?.croppedImageUrl}
            firstText={user?.fullName}
            secondText={user?.usernameAtSign}
            thirdText={user?.occupation?.label}
            fourthText={cleanRepeatedWords(user?.location?.title || '')}
            footer={
              <>
                <Flex className="px-20">
                  <Divider />
                </Flex>
                <Tabs
                  activePath={selectedTab}
                  onChangeTab={onChangeTab}
                  tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
                />
              </>
            }
          />
        )}
        <Panels tab={selectedTab} isUserPortal={isUserPortal} />
      </YouContext>
    </Flex>
  );
}

const Panels = ({
  tab,
  isUserPortal,
}: {
  tab: PortalPanelsTabkeys;
  isUserPortal: boolean;
}) => {
  switch (tab) {
    case 'teamMembers': {
      return <YouTeamMembersTab />;
    }
    case 'projects': {
      return <YouProjectsTab />;
    }
    case 'jobs': {
      return <YouJobsTab />;
    }
    case 'applicants': {
      return <YouApplicantsTab />;
    }
    case 'candidates': {
      return <YouCandidatesTab />;
    }
    case 'todos': {
      return <YouTodosTab />;
    }
    case 'meetings': {
      return <YouMeetingsTab />;
    }
    case 'notes': {
      return <YouNotesTab />;
    }
    case 'threads': {
      return <CommingSoon />;
    }
    case 'reviews': {
      return <YouReviewsTab />;
    }
    case 'assessments': {
      return <CommingSoon />;
    }
    case 'activities': {
      return <YouActivitiesTab />;
    }
    case 'insights': {
      return <CommingSoon />;
    }
    default: {
      return !isUserPortal ? <YouTeamMembersTab /> : <YouProjectsTab />;
    }
  }
};

const CommingSoon = () => {
  const { t } = useTranslation();
  return (
    <EmptySectionInModules
      title={t('coming_3dot')}
      text={t('w_r_w_o_it')}
      isFullParent
      classNames={{
        container: '!my-0',
      }}
    />
  );
};
