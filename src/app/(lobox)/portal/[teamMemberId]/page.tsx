'use client';
import Button from '@shared/uikit/Button';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import { getUser } from '@shared/utils/api/user';
import { QueryKeys } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { YouContainer } from 'app/(lobox)/portal/You';
import { useParams } from 'next/navigation';
import React from 'react';

export default function UserPortalPage() {
  const history = useHistory();
  const handleBack = () => history.goBack();
  const { t } = useTranslation();
  const params = useParams();

  const { data: user, isLoading: isLoadingUser } = useReactQuery({
    action: {
      apiFunc: getUser,
      key: [QueryKeys.getUser, params?.teamMemberId],
      params: {
        containsCroppedHeaderImageLink: true,
        userId: params?.teamMemberId,
      },
    },
    config: {
      enabled: Boolean(params?.teamMemberId),
    },
  });

  return (
    <>
      <ModalHeaderSimple
        backButtonProps={{ onClick: handleBack }}
        title={user?.fullName}
        noCloseButton
        hideBack={false}
        rightContent={() => (
          <Button
            schema="semi-transparent"
            label={t('edit')}
            leftIcon="pen"
            className="ml-auto"
          />
        )}
      />
      <YouContainer isUserPortal user={user} isLoading={isLoadingUser} />;
    </>
  );
}
