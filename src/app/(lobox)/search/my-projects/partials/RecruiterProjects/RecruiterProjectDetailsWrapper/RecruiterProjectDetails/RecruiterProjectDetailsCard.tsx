import { type FC } from 'react';
import BaseProjectCardInList from '@shared/components/molecules/ProjectCard/BaseProjectCardInList';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Tooltip from '@shared/uikit/Tooltip';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Tabs from 'shared/components/Organism/Tabs';
import RecruiterProjectDetailsCardMoreOptions from './RecruiterProjectDetailsCard/RecruiterProjectDetailsCardMoreOptions';
import classes from './RecruiterProjectDetailsStyles.module.scss';
import type { CardWrapperProps } from '@shared/components/molecules/CardItem/CardWrapper';
import type { ProjectProps } from '@shared/types/project';
import type { RecruiterProjectDetailsTabsProps } from '@shared/types/projectsProps';

export interface RecruiterProjectDetailsCardProps {
  project: ProjectProps;
  onChangeTab: (tab: string) => void;
  selectedTab: string;
  cardProps?: CardWrapperProps;
}

const RecruiterProjectDetailsCard: FC<RecruiterProjectDetailsCardProps> = ({
  project,
  onChangeTab,
  selectedTab,
  cardProps,
}) => {
  const { t } = useTranslation();
  const globalDispatch = useGlobalDispatch();

  const handleAddJob = () =>
    openMultiStepForm({
      formName: 'linkJobForm',
      data: {
        id: project.id,
        target: 'project',
        cardProps: {
          title: project.title,
          text: project.pageInfo?.title,
          icon: 'projects-light',
          jobs: project.jobs,
        },
        initialJobs: project.jobs?.map((job) => ({ id: job.id })) ?? [],
      },
    });
  const handleOpenEdit = () =>
    openMultiStepForm({
      formName: 'createProjectForm',
      data: project,
    });

  return (
    <BaseProjectCardInList
      id={project.id}
      title={project.title}
      user={project.user as any}
      lastActivity={project?.lastActivity}
      applicantsCount={Number(project.applicantsCount || 0)}
      collaboratorsCount={project.collaboratorUsers?.length}
      jobsCount={Number(project?.jobs?.length || 0)}
      time={project.lastModifiedDate}
      cardProps={cardProps}
      size="lg"
      actions={
        <Flex className="!flex-row gap-12">
          <Button
            label={t('edit')}
            schema="semi-transparent"
            leftIcon="pen-light"
            leftType="far"
            fullWidth
            onClick={handleOpenEdit}
          />
          <Button
            label={t('link_jobs')}
            leftIcon="link-rotate"
            leftType="far"
            fullWidth
            onClick={handleAddJob}
          />
          <Tooltip
            placement="top"
            trigger={
              <IconButton
                name="assignees"
                type="far"
                variant="rectangle"
                colorSchema="graySecondary"
                onClick={() =>
                  globalDispatch({
                    type: 'OPEN_EDIT_ASSIGNEE_MODAL',
                    payload: { entity: project, type: 'project' },
                  })
                }
              />
            }
          >
            {t('add_assignee')}
          </Tooltip>
        </Flex>
      }
      moreOptions={<RecruiterProjectDetailsCardMoreOptions project={project} />}
      status={project.status}
      assignees={project.collaboratorUsers}
      badgeActionsData={{
        onClickJobs: () => onChangeTab('jobs'),
        onClickApplicants: () => onChangeTab('applicants'),
        onClickAssignees: () => onChangeTab('assignees'),
      }}
    >
      <Tabs
        activePath={selectedTab}
        onChangeTab={onChangeTab}
        styles={{
          tabsRoot: classes.tabsRoot,
          linksRoot: classes.linksRoot,
        }}
        tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
      />
    </BaseProjectCardInList>
  );
};

export default RecruiterProjectDetailsCard;

const tabs: {
  path: RecruiterProjectDetailsTabsProps;
  title: RecruiterProjectDetailsTabsProps;
}[] = [
  {
    path: 'details',
    title: 'details',
  },
  {
    path: 'jobs',
    title: 'jobs',
  },
  {
    path: 'applicants',
    title: 'applicants',
  },
  {
    path: 'candidates',
    title: 'candidates',
  },
  {
    path: 'assignees',
    title: 'assignees',
  },
  {
    path: 'todos',
    title: 'todos',
  },
  {
    path: 'meetings',
    title: 'meetings',
  },
  {
    path: 'activities',
    title: 'activities',
  },
  {
    path: 'insights',
    title: 'insights',
  },
] as const;
