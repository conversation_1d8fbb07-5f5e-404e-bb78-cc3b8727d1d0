'use client';

import React from 'react';
import FormContent from '@app/settings/partials/components/FormContent/FormContent.component';
import SettingCard from '@app/settings/partials/components/SettingCard';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useSuccessUpdateSetting from 'shared/hooks/useSuccessUpdateSetting';
import Accordion from 'shared/uikit/Accordion';
import Flex from 'shared/uikit/Flex';
import RichTextView from 'shared/uikit/RichText/RichTextView';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import { db } from 'shared/utils/constants/enums';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import Cookies from 'shared/utils/toolkit/cookies';
import Endpoints from 'shared/utils/constants/endpoints';
import formatDate from 'shared/utils/toolkit/formatDate';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import QueryKeys from 'shared/utils/constants/queryKeys';
import {
  setIndustry,
  setPageName,
  setTitle,
  setCategory,
  setCompanySize,
  setDescription,
  setEstablishmentDate,
} from 'shared/utils/api/page';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import classes from './page.module.scss';
import type { BeforeCachePageDetailType } from 'shared/types/page';

const pagesCategories = collectionToObjectByKey(db.CATEGORY_TYPES);

const PageDetail = (): JSX.Element => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();

  const { businessPage } = useGetAppObject();
  const { replace } = useUpdateQueryData<Partial<BeforeCachePageDetailType>>([
    QueryKeys.businessPage,
  ]);
  const handleSuccessBuilder = useSuccessUpdateSetting({
    modifier: replace,
  });

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container title={t('page_details')}>
        <Accordion
          itemClassName={classes.itemClassName}
          contentClassName={classes.contentClassName}
          singleOpen
          data={[
            {
              id: 'page-detail-title',
              divider: true,
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('title')}
                  value={isExpanded ? undefined : businessPage?.title}
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { title: businessPage?.title },
                    apiFunc: setTitle,
                    transform: ({ title }: any) => ({
                      id: businessPage?.id,
                      title,
                    }),
                    onSuccess: handleSuccessBuilder(setExpanded),
                  }}
                  groups={[
                    {
                      name: 'title',
                      cp: 'input',
                      maxLength: 100,
                      label: t('title'),
                      required: true,
                    },
                  ]}
                />
              ),
            },
            {
              id: 'page-detail-username',
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('username')}
                  action={isExpanded ? undefined : 'editIcon'}
                  value={
                    isExpanded ? undefined : (
                      <Flex flexDir="row">
                        <Typography size={15} height={21}>
                          lobox.com/
                        </Typography>
                        <Typography size={15} height={21} color="brand">
                          {businessPage?.username}
                        </Typography>
                      </Flex>
                    )
                  }
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { username: businessPage?.username },
                    apiFunc: setPageName,
                    transform: ({ username }: any) => ({
                      id: businessPage?.id,
                      username,
                    }),
                    onSuccess: handleSuccessBuilder(
                      setExpanded,
                      ({ username }) => {
                        const BUSINESS_ID_KEY = getCookieKey('businessId');
                        Cookies.set(BUSINESS_ID_KEY, username, {
                          SameSite: 'Lax',
                        });

                        replace({ username });
                      }
                    ),
                  }}
                  groups={[
                    {
                      name: 'username',
                      cp: 'usernameInput',
                      label: t('username'),
                      forceVisibleError: true,
                      required: true,
                      url: Endpoints.Auth.validateUsername,
                      prevValue: businessPage?.username,
                    },
                  ]}
                />
              ),
              divider: true,
            },
            {
              id: 'page-detail-category',
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('category')}
                  value={
                    isExpanded ? undefined : t(businessPage?.category?.label)
                  }
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { category: businessPage?.category },
                    apiFunc: setCategory,
                    transform: ({ category }: any) => ({
                      id: businessPage?.id,
                      category: category?.value,
                    }),
                    onSuccess: handleSuccessBuilder(
                      setExpanded,
                      ({ category }) => {
                        replace({
                          category: {
                            value: category,
                            label: t(pagesCategories[category]?.label),
                          },
                        });
                      }
                    ),
                  }}
                  groups={[
                    {
                      name: 'category',
                      cp: 'dropdownSelect',
                      label: t('page_category'),
                      required: true,
                      options: db.CATEGORY_TYPES,
                    },
                  ]}
                />
              ),
              divider: true,
            },
            {
              id: 'page-detail-industry',
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('industry')}
                  value={isExpanded ? undefined : businessPage?.industry?.label}
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { industry: businessPage?.industry },
                    apiFunc: setIndustry,
                    transform: ({ industry }: any) => ({
                      id: businessPage?.id,
                      industryLookupId: industry?.value,
                      industryName: industry?.label,
                    }),
                    onSuccess: handleSuccessBuilder(
                      setExpanded,
                      ({ industryLookupId, industryName }) => {
                        replace({
                          industry: {
                            value: industryLookupId,
                            label: industryName,
                          },
                        });
                      }
                    ),
                  }}
                  groups={[
                    {
                      name: 'industry',
                      cp: 'asyncAutoComplete',
                      maxLength: 100,
                      label: t('industry'),
                      url: Endpoints.App.Common.getIndustry,
                      normalizer: lookupResponseNormalizer,
                      required: true,
                    },
                  ]}
                />
              ),
              divider: true,
            },
            {
              id: 'page-detail-description',
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('description')}
                  value={
                    isExpanded ? undefined : (
                      <RichTextView
                        className={classes.descriptionText}
                        html={businessPage?.description}
                      />
                    )
                  }
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { description: businessPage?.description },
                    apiFunc: setDescription,
                    transform: ({ description }: any) => ({
                      id: businessPage?.id,
                      description,
                    }),
                    onSuccess: handleSuccessBuilder(setExpanded),
                  }}
                  groups={({ values }: any) => [
                    {
                      name: 'description',
                      cp: 'richtext',
                      showEmoji: false,
                      label: t('description'),
                      forceVisibleError: !!values?.description,
                      maxLength: DESCRIPTION_MAX_LENGTH,
                    },
                  ]}
                />
              ),
              divider: true,
            },
            {
              id: 'page-detail-establishment-date',
              header: ({ isExpanded }) => (
                <SettingCard
                  title={t('establishment_date')}
                  value={
                    isExpanded
                      ? undefined
                      : formatDate(businessPage?.establishmentDate as Date, 'y')
                  }
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: {
                      establishmentDate: businessPage?.establishmentDate,
                    },
                    apiFunc: setEstablishmentDate,
                    transform: ({ establishmentDate }: any) => ({
                      id: businessPage?.id,
                      establishmentDate,
                    }),
                    onSuccess: handleSuccessBuilder(setExpanded),
                  }}
                  groups={[
                    {
                      name: 'establishmentDate',
                      cp: 'datePicker',
                      variant: 'input',
                      picker: 'year',
                      containerWidth: isMoreThanTablet ? 360 : undefined,
                      date: businessPage?.establishmentDate
                        ? new Date(businessPage?.establishmentDate)
                        : undefined,
                      maxDate: new Date(),
                      label: t('establishment_date'),
                    },
                  ]}
                />
              ),
              divider: true,
            },
            {
              id: 'page-detail-company-size',
              header: ({ isExpanded }) => (
                <SettingCard
                  className={classes.lastItem}
                  title={t('company_size')}
                  value={
                    isExpanded ? undefined : t(businessPage?.companySize?.label)
                  }
                  action={isExpanded ? undefined : 'editIcon'}
                />
              ),
              content: ({ setExpanded }) => (
                <FormContent
                  setExpanded={setExpanded}
                  formProps={{
                    enableReinitialize: true,
                    initialValues: { companySize: businessPage?.companySize },
                    apiFunc: setCompanySize,
                    transform: ({ companySize }: any) => ({
                      id: businessPage?.id,
                      companySize: companySize.value,
                    }),
                    onSuccess: handleSuccessBuilder(
                      setExpanded,
                      ({ companySize }) => {
                        replace({
                          companySize: {
                            value: companySize,
                            label: db.COMPANY_SIZE_TYPES.find(
                              (item) => item.value === companySize
                            )?.label,
                          },
                        });
                      }
                    ),
                  }}
                  groups={[
                    {
                      name: 'companySize',
                      cp: 'dropdownSelect',
                      label: t('page_size'),
                      options: db.COMPANY_SIZE_TYPES,
                    },
                  ]}
                />
              ),
            },
          ]}
        />
      </Container>
    </PermissionsGate>
  );
};

export default PageDetail;
