import { useCallback, useEffect, useState } from 'react';
import DatepickerV3 from '@shared/components/Organism/DatepickerV3/DatepickerV3';
import {
  setDisplayingEventTypes,
  useSchedulesState,
} from '@shared/stores/schedulesStore';
import CheckBox from 'shared/uikit/CheckBox';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import { EVENT_LABELS, allEventTypes } from 'shared/utils/consts';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './SchedulesCalendarSection.module.scss';
import type { SchedulesStoreState } from '@shared/types/schedules/store';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

function SchedulesCalendarSection() {
  const { t } = useTranslation();
  const scheduelsState = useSchedulesState();
  const { viewDate, displayingEventTypes } = scheduelsState;
  const [localviewDate, setLocalviewDate] =
    useState<SchedulesStoreState['viewDate']>(viewDate);

  useEffect(() => {
    if (
      viewDate?.start?.isValid() &&
      !viewDate.start.isSame(localviewDate.start, 'day')
    )
      setLocalviewDate(viewDate);
  }, [viewDate?.start]);

  const toggleEventCheckbox = useCallback(
    (event: ScheduleEventTypes) => {
      const newDisplayingEventTypes = new Set(displayingEventTypes);
      if (newDisplayingEventTypes.has(event)) {
        newDisplayingEventTypes.delete(event);
      } else {
        newDisplayingEventTypes.add(event);
      }
      setDisplayingEventTypes(newDisplayingEventTypes);
    },
    [displayingEventTypes]
  );

  return (
    <Flex>
      <Flex className={classes.datepickerWrapper}>
        <DatepickerV3
          data={scheduelsState}
          localviewDate={localviewDate}
          onCellClick={(start) =>
            setLocalviewDate((prev) => ({ ...prev, start }))
          }
          onDateChange={(start) =>
            setLocalviewDate((prev) => ({ ...prev, start }))
          }
          numberOfCells={7 * 6}
          doNotShowActivesOfAdjacentMonths
          hideToday
        />
      </Flex>
      {allEventTypes.map((eventType) => {
        const value = displayingEventTypes.has(eventType);

        return (
          <CheckBox
            key={eventType}
            value={value}
            classNames={{
              containerRoot: classes.checkboxRoot,
              checkbox: cnj(
                classes.checkbox,
                classes[eventType?.toLowerCase()?.concat('Color')]
              ),
            }}
            label={
              <Flex
                className={classes.checkboxWrap}
                onClick={() => toggleEventCheckbox(eventType)}
              >
                <Typography color="thirdText" font="400" size={15}>
                  {t(EVENT_LABELS[eventType])}
                </Typography>
              </Flex>
            }
            onChange={() => toggleEventCheckbox(eventType)}
          />
        );
      })}
    </Flex>
  );
}

export default SchedulesCalendarSection;
