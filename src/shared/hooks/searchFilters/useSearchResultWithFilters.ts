import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  getCandidateById,
  getCandidateFilters,
  getCandidatesTodosSearch,
  getMeetingFilters,
  getTodoFilters,
  searchCandidate,
  searchCandidateMeetings,
} from '@shared/utils/api/candidates';
import { getSearchCompanyFilters } from '@shared/utils/api/company';
import {
  searchProjectFilters,
  searchProjects,
} from '@shared/utils/api/project';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { sleep } from '@shared/utils/toolkit/sleep';
import { TOP_OF_LIST_INFO_CARD } from 'shared/constants/enums';
import eventKeys from 'shared/constants/event-keys';
import { mutableStore } from 'shared/constants/mutableStore';
import { searchGroupTypes } from 'shared/constants/search';
import { useSearchDispatch } from 'shared/contexts/search/search.provider';
import useMedia from 'shared/uikit/utils/useMedia';
import jobsApi from 'shared/utils/api/jobs';
import {
  getSuggestionPages,
  getPopularPages,
  getMyFollowerPages,
  getMyFollowingPages,
} from 'shared/utils/api/network/page';
import {
  getSuggestionPeople,
  getPopularPeople,
  getMyFollowerPeople,
  getMyFollowingPeople,
  getMyFollowRequests,
  getPendingRequests,
} from 'shared/utils/api/network/people';
import {
  searchPerson,
  getSearchPersonFilters,
  getSearchPagesFilters,
  getSearchPostFilters,
  searchPages,
  searchPosts,
  getObjectById,
  searchCompanies,
} from 'shared/utils/api/search';
import { MAIN_CENTER_WRAPPER_ID } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import usePaginateQuery from 'shared/utils/hooks/usePaginateQuery';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useStateCallback from 'shared/utils/hooks/useStateCallback';
import { makeCityValue } from 'shared/utils/makeCityValue';
import event from 'shared/utils/toolkit/event';
import uuId from 'shared/utils/toolkit/uuIdGenerator';
import useGetAppObject from '../useGetAppObject';
import type { ICandidateTodo } from '@shared/types/candidates';
import type { IFeedElement } from '@shared/types/feedElement';
import type { IPage } from '@shared/types/page';
import type { IPeople } from '@shared/types/people';
import type { ProjectProps } from '@shared/types/project';
import type { ISuggestObject } from '@shared/types/search';
import type { IJob } from 'shared/types/job';

type EntityMapping = {
  posts: IFeedElement;
  pages: IPage;
  people: IPeople;
  jobs: IJob;
  candidates: IJob;
  recruiterJobs: IJob;
  todos: ICandidateTodo;
  recruiterProjects: ProjectProps;
  recruiterJobsInner: IJob;
  companies: ISuggestObject;
  pipelines: IJob;
  searchParticipationAsApplicant: any;
  searchParticipationAsCandidate: any;
  recruiterTodos: any;
  recruiterMeetings: any;
  recruiterReviews: any;
};

export type ISearchEntity = keyof EntityMapping;
export type SearchListEntityData<E extends ISearchEntity> =
  E extends ISearchEntity ? EntityMapping[E] : any;

interface Props<E> {
  entity: E;
  extraParams?: any;
  options?: {
    shouldScrollToTop?: boolean;
  };
}

const useSearchResultWithFilters = <E extends ISearchEntity>({
  entity,
  extraParams = {},
  options = { shouldScrollToTop: true },
}: Props<E>) => {
  const { isMoreThanTablet } = useMedia();
  const { handleChangeParams, allParams } = useCustomParams();
  const searchDispatch = useSearchDispatch();
  const [searchId, setSearchId] = useStateCallback();
  const isInitialCurrentEntityId = useRef<boolean>(false);
  const [isFiltersLoading, setIsFiltersLoading] = useState(false);
  const [randomId, setRandomId] = useState(uuId().create());
  const pathname = usePathname();
  const { currentEntityId, isCandidateMode, ...restOfSearchFilters } =
    allParams || {};
  const searchGroupType = !isBusinessApp
    ? allParams.searchGroupType || searchGroupTypes.ALL
    : undefined;

  const isAllSearchGroupType = searchGroupType === searchGroupTypes.ALL;
  const pageNumber = Number(allParams?.page || 0);

  const filterApiFuncs = {
    jobs: jobsApi.getSearchFilters,
    people: getSearchPersonFilters,
    pages: getSearchPagesFilters,
    companies: getSearchCompanyFilters,
    posts: getSearchPostFilters,
    candidates: getCandidateFilters,
    recruiterJobs: jobsApi.getSearchFilters,
    pipelines: jobsApi.getBusinessJobFilters,
    recruiterProjects: searchProjectFilters,
    searchParticipationAsApplicant: jobsApi.getParticipationFilters,
    searchParticipationAsCandidate: jobsApi.getParticipationFilters,
    recruiterTodos: getTodoFilters,
    recruiterMeetings: getMeetingFilters,
    recruiterReviews: jobsApi.getReviewFilters,
  } as any;

  const queryKeysMap = {
    jobs: QueryKeys.searchJobs,
    people: QueryKeys.searchUsers,
    pages: QueryKeys.searchPages,
    posts: QueryKeys.searchPosts,
    companies: QueryKeys.searchCompanies,
    candidates: QueryKeys.searchCandidates,
    recruiterJobs: QueryKeys.searchJobs,
    pipelines: QueryKeys.getPipelinesList,
    recruiterProjects: QueryKeys.getProjectsList,
    searchParticipationAsApplicant: QueryKeys.searchParticipationAsApplicant,
    searchParticipationAsCandidate: QueryKeys.searchParticipationAsCandidate,
    recruiterTodos: QueryKeys.candidateTodos,
    recruiterMeetings: QueryKeys.candidateMeetings,
    recruiterReviews: QueryKeys.searchReviews,
  };

  const queryKey = [
    (queryKeysMap as any)[entity],
    searchGroupType,
    restOfSearchFilters,
    randomId,
    pageNumber,
  ];

  mutableStore.searchQueryKey = [...queryKey, `${pageNumber}`];
  const { getAppObjectPropValue } = useGetAppObject();
  const userId = getAppObjectPropValue({
    pageKey: 'id',
    userKey: 'id',
  });
  const getCurrentEntityId = () => {
    if (!isInitialCurrentEntityId.current) return currentEntityId;

    return undefined;
  };

  const { refetch: refetchFilters, isFetching: isFetchingFilters } =
    useReactQuery({
      action: {
        apiFunc: filterApiFuncs[entity],
        key: [
          QueryKeys.getSearchFilters,
          searchId,
          searchGroupType,
          restOfSearchFilters,
        ],
        params: {
          searchId,
          isBusinessApp,
        },
      },
      config: {
        enabled: !!searchId,
        cacheTime: 0,
        staleTime: 0,
        retry: 3,
        retryDelay: 100,
        networkMode: 'always',
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        onSuccess: async (dynamicFilters) => {
          searchDispatch({
            type: 'SET_SEARCH_DYNAMIC_FILTERS',
            payload: dynamicFilterNormalizer(dynamicFilters),
          });
          setIsFiltersLoading(false);
        },
      },
    });

  const withCurrentEntity =
    (apiFunc: Function) =>
    async (props: any): Promise<Required<{ content: any[] }>> => {
      try {
        const res = await apiFunc(props);
        const { content, currentEntity } = res;
        if (!currentEntity) return res;
        const _content: typeof content = [...content];
        const selectedDataIndex = content.findIndex(
          (item: any) => item.id === currentEntity?.id
        );

        if (selectedDataIndex > -1) {
          _content.splice(selectedDataIndex, 1);
          _content.unshift(currentEntity);
        } else {
          _content.unshift(currentEntity);
          res.numberOfElements = String(Number(res.numberOfElements) + 1);
          res.totalElements = String(Number(res.totalElements) + 1);
          if (res?.empty) {
            res.empty = false;
            res.number = String(1);
            res.totalPages = String(1);
            res.pageable = {
              ...res.pageable,
              pageNumber: String(1),
              sort: {
                ...res.pageable.sort,
                empty: false,
              },
            };
            res.sort = {
              ...res.sort,
              empty: false,
            };
          }
        }

        return { ...res, content: _content };
      } catch (err) {
        throw err;
      }
    };
  const withSeparateEntity =
    (apiFunc: Function, apiFuncSingle: Function, args: any) =>
    async (props: any): Promise<Required<{ content: IJob[] }>> => {
      try {
        const hasCurrentEntityId =
          !!props?.currentEntityId || props?.params?.currentEntityId;
        const [res, currentEntityArray] = (await Promise.all(
          [apiFunc(props), hasCurrentEntityId && apiFuncSingle(args)]?.filter(
            Boolean
          )
        )) as any;

        const currentEntity = currentEntityArray?.[0] || currentEntityArray;

        if (!currentEntity) return res;

        const { content } = res as any;
        const _content: typeof content = [...content];

        const selectedDataIndex = content.findIndex(
          (item: any) => item.id === currentEntity?.id
        );

        if (selectedDataIndex > -1) {
          _content.splice(selectedDataIndex, 1);
          _content.unshift(currentEntity);
        } else {
          _content.unshift(currentEntity);
          res.numberOfElements = String(Number(res.numberOfElements) + 1);
          res.totalElements = String(Number(res.totalElements) + 1);
          if (res?.empty) {
            res.empty = false;
            res.number = String(1);
            res.totalPages = String(1);
            res.pageable = {
              ...res.pageable,
              pageNumber: String(1),
              sort: {
                ...res.pageable.sort,
                empty: false,
              },
            };
            res.sort = {
              ...res.sort,
              empty: false,
            };
          }
        }

        return { ...res, content: _content };
      } catch (err) {
        throw err;
      }
    };

  const withFilterLoading = useCallback(
    (apiFunc: Function) =>
      async (...args: any[]) => {
        setIsFiltersLoading(true);

        return await apiFunc?.(...args);
      },
    []
  );
  const withCheckTotalPages = useCallback(
    (apiFunc: Function) => async (params: any) => {
      let res = await apiFunc?.(params);
      if (pageNumber > res?.totalPages) {
        setPage(String(0));
        res = await apiFunc({ ...params, page: String(0) });
      }

      return res;
    },
    []
  );

  const searchApiFuncs = {
    jobs: {
      [searchGroupTypes.ALL]: withCurrentEntity(jobsApi.searchJob),
      [searchGroupTypes.CREATED_BY]: withCurrentEntity(jobsApi.searchJob),
      [searchGroupTypes.TOP_SUGGESTION]: withSeparateEntity(
        jobsApi.getJobsTopSuggestionList,
        jobsApi.getJobsByIds,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.POPULAR]: withSeparateEntity(
        jobsApi.getJobsPopularList,
        jobsApi.getJobsByIds,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.APPLIED]: withSeparateEntity(
        jobsApi.getJobsApplicationList,
        jobsApi.getJobsByIds,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.SAVED]: withSeparateEntity(
        jobsApi.getJobsSavedList,
        jobsApi.getJobsByIds,
        { params: { ids: [currentEntityId] } }
      ),
    },
    people: {
      [searchGroupTypes.ALL]: withCurrentEntity(searchPerson),
      [searchGroupTypes.TOP_SUGGESTION]: withSeparateEntity(
        getSuggestionPeople,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.POPULAR]: withSeparateEntity(
        getPopularPeople,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.FOLLOWERS]: withSeparateEntity(
        getMyFollowerPeople,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.FOLLOWINGS]: withSeparateEntity(
        getMyFollowingPeople,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.INCOMING_REQUESTS]: withSeparateEntity(
        async () => getMyFollowRequests({ userType: 'PERSON' }),
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.PENDING_REQUESTS]: withSeparateEntity(
        getPendingRequests,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
    },
    pages: {
      [searchGroupTypes.ALL]: withCurrentEntity(searchPages),
      [searchGroupTypes.TOP_SUGGESTION]: withSeparateEntity(
        getSuggestionPages,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.POPULAR]: withSeparateEntity(
        getPopularPages,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.FOLLOWERS]: withSeparateEntity(
        getMyFollowerPages,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
      [searchGroupTypes.FOLLOWINGS]: withSeparateEntity(
        getMyFollowingPages,
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),

      [searchGroupTypes.INCOMING_REQUESTS]: withSeparateEntity(
        async () => getMyFollowRequests({ userType: 'PAGE' }),
        getObjectById,
        { params: { ids: [currentEntityId] } }
      ),
    },
    companies: withCurrentEntity(searchCompanies),
    posts: withCurrentEntity(searchPosts),
    candidates: withSeparateEntity(searchCandidate, getCandidateById, {
      id: currentEntityId,
    }),
    recruiterJobs: withCurrentEntity(jobsApi.getBusinessJobs),
    pipelines: jobsApi.getBusinessJobs,
    recruiterProjects: searchProjects,
    searchParticipationAsApplicant: jobsApi.searchParticipationAsApplicant,
    searchParticipationAsCandidate: jobsApi.searchParticipationAsCandidate,
    recruiterTodos: getCandidatesTodosSearch,
    recruiterMeetings: searchCandidateMeetings,
    recruiterReviews: jobsApi.searchReviews,
  } as any;

  const queryResult = usePaginateQuery<SearchListEntityData<E>>({
    action: {
      spreadParams: entity !== 'jobs',
      apiFunc: withCheckTotalPages(
        withFilterLoading(
          searchGroupType && searchApiFuncs[entity][searchGroupType]
            ? searchApiFuncs[entity][searchGroupType]
            : searchApiFuncs[entity]
        )
      ),
      key: queryKey,
      params: {
        size: 25,
        // page: 0,
        text: decodeURIComponent(allParams.query || ''),
        light: !isBusinessApp && !(entity === 'posts' && !isMoreThanTablet),
        ...(entity === 'posts' || isAllSearchGroupType || isBusinessApp
          ? {
              searchId,
              searchGroupType,
              ...restOfSearchFilters,
              isBusinessApp,
              ...extraParams,
            }
          : { ...extraParams, userId, page: pageNumber }),
        currentEntityId: getCurrentEntityId(),
        // searchEntity: entity,
      },
    },
    config: {
      onSuccess: async (res: any) => {
        if (!isInitialCurrentEntityId.current) {
          isInitialCurrentEntityId.current = true;
        }

        const firstId = res?.content?.[0]?.id;
        if (firstId) {
          setTimeout(() => {
            handleChangeParams({
              add: { currentEntityId: firstId },
              remove: ['refresh'],
            });
          }, 100);
        }

        // If filters aren't ready yet (no searchId and ALL group), set it first
        const needFilters =
          !!searchId || (searchGroupType && !isAllSearchGroupType);
        if (!needFilters) {
          // Let React Query auto-run via enabled: !!searchId so retry applies
          setSearchId(res?.searchId);
        }
        await sleep(200);
        await refetchFilters();
        await sleep(300);
        await refetchFilters();
        options.shouldScrollToTop && scrollListToTop(isMoreThanTablet);
      },

      retry: 0,
      cacheTime: 0,
      staleTime: 0,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    },
  });

  useEffect(() => {
    // This is for handling isFiltersLoading state when pressing back and forward buttons ...
    if (isFiltersLoading && !(queryResult?.isFetching || isFetchingFilters)) {
      setIsFiltersLoading(false);
      const _searchId = queryResult?.searchId;
      if (searchId !== _searchId) {
        setSearchId(_searchId);
      }
    }
  }, [isFetchingFilters, queryResult?.isFetching, isFiltersLoading]);

  const setPage = (pgNumber: string | number) => {
    handleChangeParams({
      add: { page: String(pgNumber) },
      remove: ['currentEntityId'],
    });
  };

  useEffect(() => {
    function callback() {
      isInitialCurrentEntityId.current = false;
      setRandomId(uuId().create());
    }
    event.on(eventKeys.refetchSearchWithCurrentEntityId, callback);

    return () => {
      event.off(eventKeys.refetchSearchWithCurrentEntityId);
    };
  }, [queryResult]);

  useEffect(() => {
    isInitialCurrentEntityId.current = false;
  }, [pathname]);

  const callBackHandler = () => {
    queryResult.refetch();
  };
  useEffect(() => {
    event.on(eventKeys.scrollToTopFeedList, callBackHandler);

    return () => event.off(eventKeys.scrollToTopFeedList);
  }, []);

  const overAllLoading =
    queryResult?.isFetching || queryResult?.status === 'loading';

  const _isFetchingFilters =
    overAllLoading || isFiltersLoading || queryResult.isFetching;

  useEffect(() => {
    if (allParams.shouldRefresh === 'true') {
      queryResult.refetch();
      handleChangeParams({
        remove: ['refresh'],
      });
    }
  }, [allParams.shouldRefresh]);

  return {
    ...queryResult,
    isEmpty: queryResult?.isEmpty ?? queryResult?.totalElements === 0,
    isFetching: overAllLoading,
    setPage,
    isFetchingFilters: _isFetchingFilters,
    queryKey,
  };
};

export default useSearchResultWithFilters;

function dynamicFilterNormalizer(filters: any) {
  return {
    ...filters,
    cities: filters?.cities?.map?.((item: any) => ({
      ...item,
      value: makeCityValue(item?.label, item?.value),
    })),
  };
}

function scrollListToTop(isDesktop: boolean) {
  if (isDesktop) {
    document
      .querySelector(`[data-name=${TOP_OF_LIST_INFO_CARD}]`)
      ?.scrollIntoView();
  } else {
    document.getElementById(MAIN_CENTER_WRAPPER_ID)?.scrollTo({
      top: 0,
    });
  }
}
