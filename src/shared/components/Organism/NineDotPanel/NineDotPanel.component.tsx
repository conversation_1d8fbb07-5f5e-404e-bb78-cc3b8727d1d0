import dropRight from 'lodash/dropRight';
import last from 'lodash/last';
import uniqBy from 'lodash/uniqBy';
import React, { type FC, type MouseEvent, useEffect, useState } from 'react';
import NineDotPanelSteps from '@shared/constants/NineDotPanelSteps';
import useGetFullAccessibilityGide from '@shared/hooks/api-hook/useGetFullAccessibilityGide';
import {
  closeNineDotPanel,
  selectData,
  selectDefaultActiveStep,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import ComingSoon from '@shared/svg/ComingSoon';
import { type PageAccessibilityType } from '@shared/types/page';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import ObjectCardSkeleton from 'shared/components/molecules/ObjectCard/ObjectCard.skeleton';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import FeedbackModal from 'shared/components/Organism/FeedbackModal';
import CreatePageWithTooltip from 'shared/components/Organism/NineDotPanel/partials/CreatePageWithTooltip';
import PortalProfiles from 'shared/components/Organism/PortalProfiles';
import {
  pageStatus,
  TOP_NINE_DOT_BUTTON_CLASSNAME,
} from 'shared/constants/enums';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { getPortal, isBusinessApp } from 'shared/utils/getAppEnv';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import NineDotPanelList from './NineDotPanelList.component';
import classes from './NineDotPanelList.component.module.scss';
import Billing from './partials/Billing';
import BusinessCatalogs from './partials/BusinessCatalogs';
import BusinessContactForm from './partials/BusinessContactForm';
import GetBusiness from './partials/GetBusiness';
import Plans from './partials/Plans';
import PortalCatalogs from './partials/PortalCatalogs';
import PortalCatalogsFooter from './partials/PortalCatalogsFooter';
import SupportTickets from './partials/SupportTickets';
import UserCatalogs from './partials/UserCatalogs';
import type { NineDotsMainStepsType } from '@shared/components/Organism/NineDotPanel/types';

const NineDotPanel: FC = () => {
  const { t } = useTranslation();
  const defaultActiveStep = useNineDotPanelState(selectDefaultActiveStep);
  const state = useNineDotPanelState(selectData);
  const selectedPageId = state?.pageId;
  const [selectedPortal, setSelectedPortal] = useState<string>();
  const [cashedData, setCashedData] = useState<NineDotsMainStepsType[]>([
    defaultActiveStep || NineDotPanelSteps.LIST,
  ]);
  const isFirsStep = cashedData.length === 1;
  const { data = [], isLoading } = useGetFullAccessibilityGide();
  let pages: PageAccessibilityType[] = [];

  if (selectedPageId) {
    pages = data.reduce<PageAccessibilityType[]>((prev, curr) => {
      if (selectedPageId === curr.id && curr.status === pageStatus.PUBLISHED) {
        const currentPages = curr.portalAccesses.map((item) => ({
          id: curr.id,
          title: curr.title,
          username: curr.username,
          status: curr.status,
          role: item.role,
          portal: item.portal,
          croppedImageUrl: curr.croppedImageUrl,
          croppedHeaderImageLink: curr.croppedHeaderImageLink,
          pageMemberships: curr.pageMemberships.filter(
            (pageMembershipsItem) => pageMembershipsItem.status === 'ACCEPTED'
          ),
        }));

        return [...prev, ...currentPages];
      }

      return prev;
    }, []);
  } else {
    pages = data.reduce<PageAccessibilityType[]>((prev, curr) => {
      const page = curr.portalAccesses.find(
        (i) => i.portal === selectedPortal?.toUpperCase()
      );
      if (page && curr.status === pageStatus.PUBLISHED) {
        const item: PageAccessibilityType = {
          id: curr.id,
          title: curr.title,
          username: curr.username,
          status: curr.status,
          role: page.role,
          portal: page.portal,
          croppedImageUrl: curr.croppedImageUrl,
          croppedHeaderImageLink: curr.croppedHeaderImageLink,
        };

        return [...prev, item];
      }

      return prev;
    }, []);
  }
  pages = uniqBy(pages, 'portal');
  useEffect(() => {
    if (defaultActiveStep) {
      setCashedData([defaultActiveStep]);
    }
  }, [selectedPageId, defaultActiveStep]);

  const onClose = (e?: MouseEvent<any>) => {
    const shouldIgnore = [
      ...Array.from(
        document.getElementsByClassName(TOP_NINE_DOT_BUTTON_CLASSNAME)
      ),
    ]?.some((node) => node?.contains((e?.target || null) as Element));
    if (shouldIgnore) return;
    closeNineDotPanel();
  };

  const handlePrev = (e?: MouseEvent<any>) => {
    if (isFirsStep) {
      onClose(e);
    }
    setCashedData(dropRight(cashedData));
  };
  const handleNext = (step?: NineDotsMainStepsType) => {
    step && setCashedData((prev) => [...prev, step]);
  };
  const onClickPortal = (portal: string) => {
    setSelectedPortal(portal);
    handleNext(NineDotPanelSteps.PORTAL_CATALOG);
  };
  const handleClickListItem = (step?: NineDotsMainStepsType) => {
    if (isBusinessApp) {
      setSelectedPortal(getPortal());
    }
    handleNext(step);
  };

  const steps = {
    [NineDotPanelSteps.LIST]: {
      cp: (
        <NineDotPanelList
          Steps={NineDotPanelSteps}
          onClick={handleClickListItem}
          onClose={handlePrev}
        />
      ),
      title: t('more'),
    },
    [NineDotPanelSteps.USER_CATALOG]: {
      cp: <UserCatalogs />,
      title: t('user_catalog'),
    },
    [NineDotPanelSteps.BUSINESS_CATALOG]: {
      cp: <BusinessCatalogs onClick={onClickPortal} />,
      title: t('business_catalogs'),
      footer: <CreatePageWithTooltip onClose={onClose} />,
    },
    [NineDotPanelSteps.PORTAL_CATALOG]: {
      cp: <PortalCatalogs portal={selectedPortal} />,
      title: (
        <ParseTextStringCP
          textProps={{ font: '700', color: 'smoke_coal', size: 24, height: 32 }}
          textString={translateReplacer(t('portal_catalog'), [
            t(selectedPortal),
          ])}
        />
      ),
      footer: (
        <PortalCatalogsFooter
          selectedPortal={selectedPortal}
          pages={pages}
          handleNext={handleNext}
        />
      ),
    },
    [NineDotPanelSteps.PROFILES]: {
      cp: (
        <PortalProfiles
          pages={pages}
          isLoading={isLoading}
          hasDifferentPages={cashedData?.length === 4}
        />
      ),
      title: t('business_sections'),
      footer: !isFirsStep ? <CreatePageWithTooltip onClose={onClose} /> : null,
    },
    [NineDotPanelSteps.BUSINESS_CONTACT]: {
      customRender: <BusinessContactForm onBack={handlePrev} />,
      title: t('business_contact'),
    },
    [NineDotPanelSteps.GET_BUSINESS]: {
      customRender: (
        <GetBusiness
          Steps={NineDotPanelSteps}
          onClick={handleClickListItem}
          onClose={handlePrev}
        />
      ),
      title: t('get_business'),
    },
    [NineDotPanelSteps.SUPPORT]: {
      customRender: <SupportTickets onBack={handlePrev} />,
      title: t('support'),
    },

    [NineDotPanelSteps.FEEDBACK_MODAL]: {
      customRender: (
        <FeedbackModal
          isLanding
          footerClassName={classes.footer}
          className={classes.feedBackModal}
          onclose={onClose}
        />
      ),
      title: t('support'),
    },
    [NineDotPanelSteps.PLANS]: {
      customRender: (
        <Plans
          data={data}
          handleNext={handleNext}
          onClose={handlePrev}
          isLoadingAccess={isLoading}
        />
      ),
      title: t('plans'),
    },
    [NineDotPanelSteps.BILLINGS]: {
      customRender: (
        <Billing data={data} handleNext={handleNext} onClose={handlePrev} />
      ),
      title: t('billings'),
    },
    [NineDotPanelSteps.COMING]: {
      customRender: (
        <EmptySectionInModules
          title={t('coming_up')}
          text={t('w_r_w_o_it')}
          image={<ComingSoon />}
          isFullParent
          classNames={{ container: classes.container }}
        />
      ),
      title: t('coming_up'),
    },
  };

  const { title, cp, footer, customRender } = steps[last(cashedData)] || {};

  if (!cp && !customRender) {
    return null;
  }

  return (
    <FixedRightSideModal
      onBack={handlePrev}
      onClose={handlePrev}
      onClickOutside={handlePrev}
      isOpenAnimation
    >
      <ModalHeaderSimple
        title={title}
        hideBack={isFirsStep}
        noCloseButton={!isFirsStep}
      />
      {customRender || (
        <>
          <ModalBody>
            {isLoading ? (
              <ObjectCardSkeleton variant="portalItem" isPage />
            ) : (
              cp
            )}
          </ModalBody>
          {footer}
        </>
      )}
    </FixedRightSideModal>
  );
};

export default NineDotPanel;
