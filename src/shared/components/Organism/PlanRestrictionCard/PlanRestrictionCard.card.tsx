import React, { useMemo } from 'react';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import { StyledLogo } from '@shared/svg/LogoIcon';
import { type RoleType } from '@shared/types/page';
import { getPortal } from '@shared/utils/getAppEnv';
import useBusinessPage from '@shared/utils/hooks/useBusinessPage';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { plans } from '@shared/utils/normalizers/plansNormalizer';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Flex, { type FlexProps } from 'shared/uikit/Flex/';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import type { PlanRestrictionPayload } from '@shared/types/planRestriction';

const administrativeRoles: RoleType[] = ['ADMIN', 'OWNER', 'HEAD'];

type PlanLower = 'free' | 'standard' | 'premium' | 'enterprise' | 'trial';

const PLAN_STYLES: Record<
  PlanLower,
  { wrapper: string; badge: string; title: string }
> = {
  free: {
    wrapper:
      'bg-blue-500/5 dark:bg-blue-400/10 border border-blue-400/10 shadow-sm',
    badge: 'bg-blue-500 text-white dark:bg-blue-400 ',
    title: 'text-white',
  },
  standard: {
    wrapper:
      'bg-zinc-700/40 dark:bg-zinc-800/60 border border-zinc-700/60 shadow-sm',
    badge: 'bg-zinc-300 text-zinc-900 ',
    title: 'text-white',
  },
  premium: {
    wrapper:
      'bg-yellow-500/10 dark:bg-yellow-400/10 border border-yellow-500/20 shadow-sm',
    badge: 'bg-yellow-500 text-zinc-900 ',
    title: 'text-white',
  },
  enterprise: {
    wrapper:
      'bg-zinc-700/30 dark:bg-zinc-800/50 border border-zinc-700/60 shadow-sm',
    badge: 'bg-zinc-300 text-zinc-900 ',
    title: 'text-white',
  },
  trial: {
    wrapper:
      'bg-blue-500/5 dark:bg-blue-400/10 border border-blue-400/10 shadow-sm',
    badge: 'bg-blue-500 text-white dark:bg-blue-400 ',
    title: 'text-white',
  },
};
interface Props extends FlexProps {
  feature: PlanRestrictionPayload;
}
export default function PlanRestrictionCardComponent({
  className,
  feature,
  ...props
}: Props) {
  const { t } = useTranslation();
  const appPortal = getPortal().toUpperCase();
  const { data: businessPageData } = useBusinessPage({ isEnabled: true });
  const { planName = '', planAllow, featureName } = feature;

  const haveAdminAccess = useMemo(
    () =>
      !!businessPageData?.myMemberships?.find(({ role }) =>
        administrativeRoles.includes(role)
      ),
    [businessPageData?.myMemberships]
  );

  const planLower = planName.toLowerCase();

  const styles = PLAN_STYLES[planLower] || {};

  const planKey = planName?.toUpperCase?.() as keyof typeof plans;
  const PlanLogo = plans[planKey]?.Logo || StyledLogo;

  const message = useMemo(() => {
    const limitLabel = planAllow ? t('limited_access') : t('no_access');
    const planText = t(planLower);
    const featureText = t(featureName);
    const portalText = t(appPortal);
    const key = haveAdminAccess
      ? 'paywall_message_admin'
      : 'paywall_message_non_admin';

    return translateReplacer(t(key), [
      planText,
      limitLabel,
      featureText,
      portalText,
    ]);
  }, [haveAdminAccess, planAllow, featureName, appPortal, planLower, t]);

  const handleOpenPlans = () => {
    openNineDotPanel({
      isOpen: true,
      defaultActiveStep: 'PLANS',
    });
  };

  return (
    <Flex
      {...props}
      className={cnj(
        '!flex-row gap-8 items-start w-full p-12 rounded-md',
        styles?.wrapper,
        className
      )}
    >
      <div
        className={cnj(
          styles?.badge,
          'rounded-md w-24 h-24 flex items-center justify-center'
        )}
        aria-hidden
      >
        <PlanLogo />
      </div>

      <Flex className="flex-1 flex flex-col gap-1">
        <Flex className="!flex-row items-center justify-between">
          <Typography font="700" size={15} className={styles?.title}>
            {t('upgrade_plan')}
          </Typography>

          {haveAdminAccess && (
            <Typography
              as="button"
              onClick={handleOpenPlans}
              color="brand"
              font="700"
              className="text-right"
            >
              {t('compare_and_upgrade')}
            </Typography>
          )}
        </Flex>

        <Typography size={14} className="text-zinc-200">
          {message}
        </Typography>
      </Flex>
    </Flex>
  );
}
