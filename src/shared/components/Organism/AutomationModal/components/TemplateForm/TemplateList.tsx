import React from 'react';
import { TemplateEmptyState } from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateEmpty';
import TemplateItem from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateItem';
import TemplateLoadingSkeleton from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateLoading';
import Flex from '@shared/uikit/Flex';
import useDisclosure from '@shared/utils/hooks/useDisclosure';
import TemplateSearch from './TemplateSearch';
import type { TemplateListProps } from '@shared/components/Organism/AutomationModal/types/template.types';

const TemplateList: React.FC<TemplateListProps> = ({
  templates,
  isLoading = false,
  searchQuery = '',
  onSearchChange,
  onTemplateClick,
  onSetDefault,
  actions = [],
  config = {},
  isUpdatingDefault,
  defaultTemplateId,
  checkingDefaultWithApi = true,
  matchesDefaultFlag = false,
  isDefaultTemplateLoading,
}) => {
  const { isOpen, onClose, onOpen } = useDisclosure();

  const {
    searchPlaceholder,
    showSearch = true,
    showActions = true,
    showDefaultToggle = true,
    emptyStateMessage,
  } = config;

  return (
    <Flex className="flex-col h-full gap-8">
      <Flex className="flex-col h-full gap-20">
        {showSearch && onSearchChange && (
          <TemplateSearch
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={onSearchChange}
          />
        )}

        <Flex className="flex-col gap-12 flex-1 overflow-y-auto">
          {isLoading ? (
            <TemplateLoadingSkeleton count={10} />
          ) : !templates.length ? (
            <TemplateEmptyState message={emptyStateMessage} />
          ) : (
            templates.map((template) => (
              <TemplateItem
                key={template.id}
                template={template}
                defaultTemplateId={defaultTemplateId}
                matchesDefaultFlag={matchesDefaultFlag}
                checkingDefaultWithApi={checkingDefaultWithApi}
                showActions={showActions}
                showDefaultToggle={showDefaultToggle}
                actions={actions}
                isOpen={isOpen}
                onOpen={onOpen}
                onClose={onClose}
                onTemplateClick={onTemplateClick}
                onSetDefault={onSetDefault}
                isUpdatingDefault={isUpdatingDefault}
              />
            ))
          )}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default TemplateList;
