import React from 'react';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import MenuItem from '@shared/uikit/MenuItem';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import TemplateActions from './TemplateActions';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';

export interface TemplateItemProps {
  template: NormalizedTemplate;
  defaultTemplateId?: string | null;
  matchesDefaultFlag?: boolean;
  checkingDefaultWithApi?: boolean;
  showActions?: boolean;
  showDefaultToggle?: boolean;
  actions?: TemplateAction[];
  isOpen?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  onTemplateClick?: (templateId?: string) => void;
  onSetDefault?: (
    templateId: string,
    isDefault: boolean,
    event?: React.MouseEvent
  ) => void;
  isUpdatingDefault?: string | null;
}

const TemplateItem: React.FC<TemplateItemProps> = ({
  template,
  defaultTemplateId,
  matchesDefaultFlag = false,
  checkingDefaultWithApi = true,
  showActions = true,
  showDefaultToggle = true,
  actions = [],
  isOpen = false,
  onOpen,
  onClose,
  onTemplateClick,
  onSetDefault,
  isUpdatingDefault,
}) => {
  const { t } = useTranslation();

  const matchesDefaultId =
    template?.id && defaultTemplateId && template?.id === defaultTemplateId;
  const hasDefaultFlag = template.default || matchesDefaultFlag;

  const isDefault = checkingDefaultWithApi
    ? Boolean(matchesDefaultId && hasDefaultFlag)
    : hasDefaultFlag;

  return (
    <MenuItem
      key={template.id}
      title={template?.title}
      titleClassName="text-lg font-semibold"
      actionClassName="justify-end"
      subTitle={
        <Typography className="flex flex-col mt-4 text-xs whitespace-nowrap">
          <Typography className="!text-colorIconForth2 text-sm font-medium">
            {t('subject')}:
          </Typography>
          <Typography
            isTruncated
            className="text-primaryText text-xs font-normal"
          >
            {template.subject}
          </Typography>
        </Typography>
      }
      className="!rounded !p-12 overflow-hidden flex justify-between items-center border border-solid !border-techGray_20 !bg-gray_5 hover:border-brand hover:bg-brand-50 shrink-0"
      onClick={() => onTemplateClick?.(template?.id)}
      actionElement={
        <Flex className="justify-between items-end gap-12 h-full">
          {showActions && actions.length > 0 ? (
            <TemplateActions
              template={template}
              actions={actions}
              isOpen={isOpen}
              onOpen={onOpen}
              onClose={onClose}
            />
          ) : (
            <Flex />
          )}
          {showDefaultToggle && onSetDefault && (
            <Button
              schema={isDefault ? 'success-semi-transparent' : 'secondary-dark'}
              variant="thin"
              label={isDefault ? t('default') : t('set_as_default')}
              leftIcon={isDefault ? 'check' : undefined}
              leftType="fas"
              labelColor={isDefault ? 'success' : 'primaryText'}
              onClick={(e) => {
                e?.stopPropagation();
                onSetDefault(template.id, isDefault, e);
              }}
              disabled={isUpdatingDefault === template.id}
              isLoading={isUpdatingDefault === template.id}
            />
          )}
        </Flex>
      }
    />
  );
};

export default TemplateItem;
