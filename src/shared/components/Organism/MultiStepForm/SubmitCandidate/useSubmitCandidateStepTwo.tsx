import { useCallback } from 'react';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import CheckBox from '@shared/uikit/CheckBox';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { getCandidatesList } from '@shared/utils/api/candidates';
import preventClickHandler from '@shared/utils/toolkit/preventClickHandler';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import Flex from 'shared/uikit/Flex';
import { QueryKeys } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { CandidateFormData } from '@shared/types/candidates';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';

type SingleDataItem = MultiStepFormStepProps & {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useSubmitCandidateStepTwo(): SingleDataItem[] {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { data: submitCandidate } = useMultiStepFormState('submitCandidate');
  const isEditMode = submitCandidate?.isEdit;

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex className="!flex-row gap-8">
      <Button
        fullWidth
        onClick={() => setStep((prev) => prev - 1)}
        label={t('discard')}
        schema="gray-semi-transparent"
      />
      <SubmitButton fullWidth label={t('submit')} />
    </Flex>
  );

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: isEditMode ? t('edit_submissions') : t('submit_candidate'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
  });

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: t('select_candidates'),
    iconProps: {
      name: 'users',
      type: 'fal',
    },
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string) => {
      if (candidateId) {
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            id: candidateId,
            enableNavigate: false,
          },
        });
      }
    },
    [appDispatch]
  );

  const handleCandidateToggle = useCallback(
    (candidateId: string, setFieldValue: any, formValues: any) => {
      const currentCandidates = formValues.candidateIds || [];
      const isSelected = currentCandidates.includes(candidateId);

      if (isSelected) {
        // Remove candidate
        const updatedCandidates = currentCandidates.filter(
          (id: string) => id !== candidateId
        );
        setFieldValue('candidateIds', updatedCandidates);
      } else {
        // Add candidate
        const updatedCandidates = [...currentCandidates, candidateId];
        setFieldValue('candidateIds', updatedCandidates);
      }
    },
    []
  );
  const viewDetailsHandler = (e, id: string) => {
    preventClickHandler(e);
    handleOpenManagerModal(id);
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,
      getStepHeaderProps,
      renderBody: ({ values: formValues, setFieldValue }) => (
        <SearchableAsyncList<CandidateFormData>
          variant="multi"
          name={QueryKeys.suggestCandidates}
          listItemsClassName="mt-12"
          apiFunc={getCandidatesList}
          params={{ jobId: formValues.job?.id }}
          normalizer={(values) => values?.content || []}
          placeholder={t('search_candidates')}
          keywords="text"
          pageSize={20}
          containerHeightOffset={300}
          enableInfiniteScroll
          renderItem={({ item }) => {
            const isSelected =
              item.submitted || formValues.candidateIds.includes(item.id);

            return (
              <ItemComponent
                key={item.id}
                image={item.profile?.croppedImageUrl}
                title={item.profile?.fullName}
                subTitle={item.profile?.usernameAtSign}
                isCompany={false}
                onClick={() =>
                  handleCandidateToggle(item.id, setFieldValue, formValues)
                }
              >
                <Flex className="!flex-row gap-8 items-center">
                  <Button
                    onClick={(e) => viewDetailsHandler(e, item.id)}
                    label={t('view_details')}
                    schema="semi-transparent"
                  />
                  <CheckBox
                    classNames={{ root: '!ml-auto' }}
                    value={isSelected}
                  />
                </Flex>
              </ItemComponent>
            );
          }}
        />
      ),
      renderFooter,
    },
  ];

  return data;
}
