import { useMutation } from '@tanstack/react-query';
import { useState, type Dispatch, type SetStateAction } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import CheckBox from '@shared/uikit/CheckBox';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { getJobsForLinking, updateBulkCandidate } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import CandidateBulkActionSkeleton from './CandidateBulkActionSkeleton';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
  setCheckedIds: Dispatch<SetStateAction<string[]>>;
};

export function useCandidateBulkActionsStepTwo(): SingleDataItem[] {
  const { t } = useTranslation();
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const { handleSuccess } = useResponseToast();
  const [totalCount, setTotalCount] = useState('');

  const { data: candidateBulkAction } = useMultiStepFormState(
    'candidateBulkAction'
  );

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('link_jobs'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const { mutate: linkJobMutate, isLoading: isLinkJobLoading } = useMutation({
    mutationFn: updateBulkCandidate,
  });

  const onSubmit = () => {
    linkJobMutate(
      {
        candidateIds: candidateBulkAction?.candidateIds as any[],
        jobIds: checkedIds as any[],
      },
      {
        onSuccess: () => {
          handleSuccess({
            message: t('candidate_link_message'),
            title: t('candidate_link_title'),
          });
        },
      }
    );
  };

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex flexDir="column" className="gap-12">
      <Typography
        color="secondaryDisabledText"
        size={12}
        fontWeight={400}
        className="text-start"
      >
        {checkedIds?.length} {t('of')} {totalCount} {t('jobs_selected')}
      </Typography>
      <TwoButtonFooter
        submitLabel={t('link')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={!checkedIds?.length || isLinkJobLoading}
        onSubmitClick={onSubmit}
        secondaryButtonOnClick={() => setStep(0)}
      />
    </Flex>
  );

  const handleItemClick = (id: string) => {
    const isCurrentlySelected = checkedIds.includes(id);

    if (isCurrentlySelected) {
      setCheckedIds((prev) => prev.filter((val) => val !== id));
    } else {
      if (checkedIds.length >= +totalCount) return;
      setCheckedIds((prev) => [...prev, id]);
    }
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: () => (
        <SearchableAsyncList<SuggestSubmitToVendor>
          variant="multi"
          name={QueryKeys.getSuggestCompany}
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.croppedImageUrl}
              title={item.title}
              subTitle={item?.username}
              onClick={() => handleItemClick(item?.id)}
            >
              <CheckBox value={checkedIds.find((val) => item?.id === val)} />
            </ItemComponent>
          )}
          pageSize={20}
          setTotalCount={setTotalCount}
          params={{
            status: 'OPEN',
          }}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          keywords="text"
          apiFunc={getJobsForLinking}
          placeholder={t('search_submit_vendor')}
          renderNextPageLoading={<CandidateBulkActionSkeleton />}
          listItemsClassName="p-12"
          searchClassName="px-20 pt-20"
          enableInfiniteScroll
          containerHeightOffset={280}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
