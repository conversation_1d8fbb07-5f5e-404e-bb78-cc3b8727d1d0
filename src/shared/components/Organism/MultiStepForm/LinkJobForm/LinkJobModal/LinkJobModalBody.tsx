import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import Avatar from '@shared/uikit/Avatar';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import Flex from 'shared/uikit/Flex';
import classes from './LinkJobModal.module.scss';
import LinkJobModalJobsList from './LinkJobModalBody/LinkJobModalJobsList';

const LinkJobModalBody = () => {
  const { data } = useMultiStepFormState('linkJobForm');

  return (
    <Flex>
      {data?.cardProps && (
        <Flex className="px-20 pt-20">
          <Flex
            flexDir="row"
            className={classes.infoCardWrapper}
            alignItems="center"
          >
            {data?.cardProps?.image && (
              <Avatar
                imgSrc={data?.cardProps?.image}
                name={data?.cardProps?.title}
                nameAsPlaceholder
                className={classes.avatar}
                size="smd"
              />
            )}
            {!data?.cardProps?.image && data?.cardProps?.icon && (
              <Flex className={classes.avatarInnerWrapper}>
                <Icon
                  name={data?.cardProps?.icon}
                  color="smoke_coal"
                  type="far"
                  size={20}
                />
              </Flex>
            )}
            <Flex className={classes.smallGap}>
              <Typography
                size={15}
                height={18}
                font="700"
                color="smoke_coal"
                ml={8}
              >
                {data?.cardProps?.title}
              </Typography>
              <Typography size={12} height={18} font="400" color="gray" ml={8}>
                {data?.cardProps?.text}
              </Typography>
            </Flex>
          </Flex>
        </Flex>
      )}
      <LinkJobModalJobsList />
    </Flex>
  );
};

export default LinkJobModalBody;
