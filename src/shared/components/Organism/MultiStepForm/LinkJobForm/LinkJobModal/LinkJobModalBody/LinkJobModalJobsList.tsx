import { useFormikContext } from 'formik';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo } from 'react';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { getCandidatesList } from '@shared/utils/api/candidates';
import { getJobsForLinking } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import LinkJobModalCandidateItem from './LinkJobModalJobsList/LinkJobModalCandidateItem';
import LinkJobModalJobItem from './LinkJobModalJobsList/LinkJobModalJobItem';
import LinkJobModalJobsListFooter from './LinkJobModalJobsList/LinkJobModalJobsListFooter';
import type { CandidateFormData } from '@shared/types/candidates';
import type { FC } from 'react';
import type { IJobApi } from 'shared/types/job';

const LinkJobModalJobsList: FC = () => {
  const { t } = useTranslation();
  const { data } = useMultiStepFormState('linkJobForm');
  const { values, setFieldValue } = useFormikContext() as {
    values: { jobs: IJobApi[] };
    setFieldValue: (name: string, data: IJobApi[]) => void;
  };
  const params = useParams();
  const id = params?.id;

  const target = data?.target;

  const jobs = useMemo(
    () =>
      data?.cardProps?.jobs?.map((val: any) => ({
        id: val.id,
      })) ||
      data?.initialJobs?.map((val: any) => ({
        id: val.id,
      })) ||
      [],
    [data]
  );

  const apiConfig = useMemo(() => {
    if (target === 'job') {
      return {
        queryKey: QueryKeys.suggestCandidates,
        apiFunc: getCandidatesList,
        params: { onlyCandidates: true, jobId: id },
      };
    }

    return {
      queryKey: QueryKeys.getJobsList,
      apiFunc: getJobsForLinking,
      params: { status: 'OPEN' },
    };
  }, [target]);

  useEffect(() => {
    setFieldValue('jobs', jobs || []);
  }, [jobs]);

  const onClickCheckBox = useCallback(
    (item: IJobApi) => {
      const list: IJobApi[] = values?.jobs;
      const checked = list?.some((_item) => _item?.id === item?.id);

      if (checked) {
        setFieldValue(
          'jobs',
          list.filter((job) => job.id !== item.id)
        );
      } else {
        setFieldValue('jobs', list?.length ? [...list, item] : [item]);
      }
    },
    [values?.jobs, setFieldValue]
  );

  const onAddOrRemoveCandidate = useCallback(
    (candidate: CandidateFormData, action: 'add' | 'remove') => {
      const list = values.jobs as any[];

      if (action === 'add') {
        setFieldValue(
          'jobs',
          list?.length ? [...list, candidate] : [candidate]
        );
      } else
        setFieldValue(
          'jobs',
          list.filter((_candidate) => _candidate.id !== candidate.id)
        );
    },
    [values.jobs, setFieldValue]
  );

  const renderItem = useCallback(
    ({ item }: { item: any; isSelected: boolean }) => {
      const selectedItems = values.jobs || jobs;

      if (target === 'job') {
        return (
          <LinkJobModalCandidateItem
            candidate={item}
            onClick={onAddOrRemoveCandidate}
          />
        );
      }

      return (
        <LinkJobModalJobItem
          job={item}
          onClick={onClickCheckBox}
          selectedJobs={selectedItems}
        />
      );
    },
    [values.jobs, jobs, target, onAddOrRemoveCandidate, onClickCheckBox]
  );

  return (
    <SearchableAsyncList
      variant="multi"
      name={apiConfig.queryKey}
      renderItem={renderItem}
      pageSize={20}
      params={apiConfig.params}
      normalizer={(response) => response?.content || []}
      keywords="text"
      apiFunc={apiConfig.apiFunc}
      placeholder={t(placeholderText[target as keyof typeof placeholderText])}
      renderNextPageLoading={<LinkJobModalJobsListFooter />}
      renderLoading={<LinkJobModalJobsListFooter />}
      renderEmpty={
        <EmptySearchResult
          title={t('no_result_found')}
          sectionMessage={t('try_refining_search')}
          className="flex-1"
        />
      }
      listItemsClassName="p-12"
      searchClassName="px-20 pt-20"
      wrapperClassName="flex flex-col h-full"
      enableInfiniteScroll
      // maxHeight={600}
    />
  );
};

export default LinkJobModalJobsList;

const placeholderText = {
  project: 'search_jobs',
  candidate: 'search_jobs',
  job: 'search_candidates',
};
