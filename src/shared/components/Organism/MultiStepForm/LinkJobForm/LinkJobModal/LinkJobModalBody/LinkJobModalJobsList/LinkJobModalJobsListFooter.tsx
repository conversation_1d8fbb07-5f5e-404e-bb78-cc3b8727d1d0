import AvatarCardSkeleton from '@shared/uikit/AvatarCard/AvatarCard.Skeleton';
import Flex from '@shared/uikit/Flex';

const LinkJobModalJobsListFooter = (props: React.HTMLProps<HTMLDivElement>) => (
  <div {...props}>
    <Flex className="gap-20 p-20">
      {new Array(16).fill(1).map((_, i) => (
        <AvatarCardSkeleton key={`link_job_skeleton_${i}`} isPage />
      ))}
    </Flex>
  </div>
);
LinkJobModalJobsListFooter.displayName = 'LinkJobModalJobsListFooter';

export default LinkJobModalJobsListFooter;
