import dynamic from 'next/dynamic';
import React from 'react';
import type { CallbackParams } from '../../MultiStepForm';
import type { ProjectProps } from '@shared/types/project';
import type { FC } from 'react';

// check the all steps
const CreateJobStepOne = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepOne'),
  { ssr: false }
);
const CreateJobStepTwo = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepTwo'),
  { ssr: false }
);
const CreateJobStepThree = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepThree'),
  { ssr: false }
);
const CreateJobStepFour = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepFour'),
  { ssr: false }
);
const CreateJobStepFive = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepFive'),
  { ssr: false }
);
const CreateJobStepSix = dynamic(
  () => import('./CreateJobModalBody/CreateJobStepSix'),
  { ssr: false }
);

const CreateJobModalBody: FC<
  Pick<CallbackParams, 'step'> & {
    list: ProjectProps[];
    loadMore: (index: number) => void;
    hasNextPage?: boolean;
  }
> = (props) => {
  const { step, ...rest } = props;

  const layout = () => {
    switch (step) {
      case 0: {
        return <CreateJobStepOne {...rest} />;
      }
      case 1: {
        return <CreateJobStepTwo />;
      }
      case 2: {
        return <CreateJobStepThree />;
      }
      case 3: {
        return <CreateJobStepFour />;
      }
      case 4: {
        return <CreateJobStepFive />;
      }
      case 5: {
        return <CreateJobStepSix />;
      }
      default: {
        return <p>No step found!</p>;
      }
    }
  };

  return <>{layout()}</>;
};

export default CreateJobModalBody;
