import InfoCard from '@shared/uikit/InfoCard';
import Typography from '@shared/uikit/Typography';
import { getPortal } from '@shared/utils/getAppEnv';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Info from 'shared/components/molecules/Info/Info';
import JobFullDetails from 'shared/components/molecules/Job';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useCreateJobModal } from '../../CreateJobModalProvider';
import classes from './CreateJobModalBody.module.scss';
import CreateJobCard from './CreateJobStepSix/CreateJobCard';

const CreateJobStepSix = () => {
  const { job, invoice } = useCreateJobModal();
  const { t } = useTranslation();
  const portal = getPortal();

  console.log('job', job);

  // Todo: fetch payment data

  return (
    <Flex className={classes.stepSixRoot}>
      <InfoCard
        hasLeftIcon
        leftIconProps={{
          name: 'sales-light',
          color: 'secondaryDisabledText',
          size: 24,
        }}
        label={translateReplacer(t('create_job_payment_alert'), [
          'job',
          portal,
          `$${invoice?.price}`,
        ])}
        labelProps={{
          color: 'smoke_coal',
          font: '700',
        }}
        classNames={{
          wrapper: classes.paymentRequiredAlert,
          leftWrapper: classes.paymentRequiredAlertIcon,
        }}
      >
        <Typography color="secondaryDisabledText">
          {t('complete_payment_to_proceed')}
        </Typography>
      </InfoCard>
      <Info
        icon="exclamation-triangle"
        text={t('create_job_unbulish_alert')}
        color="pendingOrange"
        textColor="smoke_coal"
        className={classes.alert}
      />
      <CreateJobCard />
      {job && (
        <JobFullDetails
          job={job}
          classNames={{ sectionLayout: classes.sectionStepSixChildren }}
        />
      )}
    </Flex>
  );
};

export default CreateJobStepSix;
