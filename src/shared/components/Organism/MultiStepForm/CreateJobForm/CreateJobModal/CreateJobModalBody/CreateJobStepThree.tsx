import { useFormikContext } from 'formik';
import { motion } from 'framer-motion';
import Flex from 'shared/uikit/Flex';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Icon from 'shared/uikit/Icon';
import Tooltip from 'shared/uikit/Tooltip';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './CreateJobModalBody.module.scss';
import type { CreateJobFormDataProps } from 'shared/types/jobsProps';

const CreateJobStepThree = () => {
  const { t } = useTranslation();
  const { values } = useFormikContext<CreateJobFormDataProps>();

  return (
    <motion.div
      className={classes.wrapper}
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <DynamicFormBuilder
        className="gap-32"
        groups={[
          {
            formGroup: {
              title: t('general_info'),
              formSection: true,
            },
            name: 'description',
            cp: 'richTextEditor',
            changeWithDebounce: true,
            maxLength: 8000,
            visibleCharCounter: true,
            placeholder: t('description'),
            className: classes.editor,
            required: true,
            jobCreation: true,
          },
          {
            formGroup: {
              title: t('skills'),
              formSection: true,
              rightComponent: (
                <Flex className="ml-4">
                  <Tooltip
                    trigger={
                      <Icon
                        type="far"
                        name="info-circle"
                        size={16}
                        color="colorIconForth2"
                      />
                    }
                    placement="top"
                  >
                    {t('skills_minimum_items')}
                  </Tooltip>
                </Flex>
              ),
              classNames: {
                title: '!leading-[15px]',
              },
            },
            name: 'skills',
            cp: 'skillPicker',
            classNames: { inputWrapper: classes.skills, listWrapper: 'mb-12' },
            minSkills: 3,
          },
          {
            formGroup: {
              title: t('languages'),
              formSection: true,
            },
            name: 'languages',
            cp: 'languagePicker',
            classNames: { listWrapper: classes.noPaddingTop },
          },
          {
            name: 'hashtags',
            cp: 'hashtagPicker',
            tooltip: t('create_page_hashtag_tooltip'),
            text: 'job',
            wrapStyle: classes.paddingBottom,
            forceVisibleError: true,
            toolTipProps: {
              color: 'colorIconForth2',
            },
          },
        ]}
      />
    </motion.div>
  );
};

export default CreateJobStepThree;
