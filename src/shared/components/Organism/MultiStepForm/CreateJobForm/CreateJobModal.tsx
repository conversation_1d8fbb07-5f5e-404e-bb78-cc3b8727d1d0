import { useQueryClient } from '@tanstack/react-query';
import React, { memo, useMemo } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import { setStatus } from '@shared/utils/api/jobs';
import {
  closeMultiStepForm,
  useMultiStepFormState,
  openMultiStepForm,
} from 'shared/hooks/useMultiStepForm';
import { QueryKeys } from 'shared/utils/constants';
import getStepData from 'shared/utils/getStepData';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { type OpenCheckoutModalProps } from '../CheckoutForm';
import MultiStepForm from '../MultiStepForm';
import { useMapJobData } from './CreateJobModal/useCreateJobSubForm';
import classes from './CreateJobModal.module.scss';
import { useCreateJobModal } from './CreateJobModalProvider';
import { useCreateJobForm } from './useCreateJobForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { CreateJobAPIDataProps } from '@shared/types/jobsProps';

const CreateJobModal = () => {
  const { data: initialData } = useMultiStepFormState('createJobForm');
  const data = useCreateJobForm({ jobId: initialData?.id });
  const { t } = useTranslation();
  const { job, subForm, invoice } = useCreateJobModal();
  const onClose = () => closeMultiStepForm('createJobForm');
  const { handleSuccess, handleError } = useResponseToast();
  const queryClient = useQueryClient();
  const totalSteps = useMemo(() => data.length ?? 0, [data]);
  const getHeaderProps = getStepData('getHeaderProps', data);
  const getStepHeaderProps = getStepData('getStepHeaderProps', data);
  const renderFooter = getStepData('renderFooter', data);
  const renderBody = getStepData('renderBody', data);
  const getValidationSchema = getStepData('getValidationSchema', data);
  const { handleMapJobData } = useMapJobData();
  const apiFunc: MultiStepFormProps['apiFunc'] = async () => {
    openMultiStepForm({
      formName: 'checkout',
      data: {
        entityData: {
          entityId: job?.id,
          label: job?.title,
          subTitle: job?.pageTitle,
          priceUnit: t('per_job'),
          requestType: 'PUBLISH_JOB',
        },
        invoice: {
          taxAmount: invoice?.tax,
          pricePerSeat: invoice?.price,
          price: invoice?.price,
          requestType: 'PUBLISH_JOB',
          id: job?.id,
        },
        actions: {
          onCancel: () => {
            closeMultiStepForm('checkout');
          },
          onSuccess: async () => {
            await setStatus({ id: job?.id, status: 'OPEN' });
            onAlert(t('job_published_successfully'), 'success');
          },
        },
      },
    });
  };
  const onAlert = async (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      handleSuccess({ message, title: t('job_published') });
    } else {
      handleError({ message, title: t('job_cap') });
    }
    onClose();
    if (type === 'success') {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getJobsList],
        exact: false,
      });
      if (initialData?.id) {
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.jobDetails, String(initialData.id)],
          exact: false,
        });
      }
    }
  };
  const onFailure = (error: any) => {
    let message = '';
    if (error?.response?.data?.fieldErrors?.length > 0) {
      message = `${error.response.data.fieldErrors[0].code}: ${error.response.data.fieldErrors[0].defaultMessage}`;
    }
    onAlert(message ?? 'Error!', 'error');
  };

  return (
    <MultiStepForm
      apiFunc={apiFunc}
      formName="createJobForm"
      totalSteps={totalSteps}
      isOpenAnimation
      onClose={onClose}
      getHeaderProps={getHeaderProps}
      getStepHeaderProps={getStepHeaderProps}
      renderFooter={renderFooter}
      renderBody={renderBody}
      onFailure={onFailure}
      initialValues={
        initialData
          ? handleMapJobData(initialData as CreateJobAPIDataProps)
          : defaultValues
      }
      getValidationSchema={getValidationSchema}
      enableReinitialize={false}
      bodyProps={{
        className: subForm === 'questions' ? classes.noPadding : '',
      }}
      customConfirmationProps={
        initialData?.id || job?.id
          ? {
              description: t('unpublish_job_alert'),
            }
          : undefined
      }
    />
  );
};

export default memo(CreateJobModal);

const workDays = [
  { label: 'MON', value: 'MONDAY' },
  { label: 'TUE', value: 'TUESDAY' },
  { label: 'WED', value: 'WEDNESDAY' },
  { label: 'THU', value: 'THURSDAY' },
  { label: 'FRI', value: 'FRIDAY' },
];

const defaultValues = {
  projects: [],
  collaboratorUsers: [],
  priority: { value: 'MEDIUM', label: 'medium' },
  skills: [],
  languages: [],
  emailRequired: true,
  phoneRequired: true,
  resumeRequired: true,
  coverLetterRequired: false,
  workDays,
  workAuthorizations: [{}],
  locationWithExtraParams: null,
};
