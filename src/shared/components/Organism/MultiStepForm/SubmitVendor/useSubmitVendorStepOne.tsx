import { type Dispatch, type SetStateAction } from 'react';
import useBackToModal from '@shared/hooks/useBackToModal';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import CheckBox from '@shared/uikit/CheckBox';
import MenuItem from '@shared/uikit/MenuItem';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import {
  getSuggestCompany,
  getVendorsExcluded,
} from '@shared/utils/api/company';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import eventKeys from 'shared/constants/event-keys';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import SubmitVendorItemSkeleton from './SubmitVendorItemSkeleton';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
  setCheckedIds: Dispatch<SetStateAction<string[]>>;
};
const MAXIMUM_SUBMISSION = 50;

export function useSubmitVendorStepOne({
  checkedIds,
  setCheckedIds,
}: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');

  const { data: submitVendorData } = useMultiStepFormState('submitToVendor');

  const { data: excludedJobs, isLoading: isLoadingExcluded } =
    useReactQuery<any>({
      action: {
        apiFunc: () => getVendorsExcluded(submitVendorData?.jobId),
        key: [QueryKeys.getVendorsExcluded, String(submitVendorData?.jobId)],
      },
      config: {
        onSuccess: (data) => {
          setCheckedIds(data);
        },
      },
    });

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('submit_to_vendor'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    backButtonProps: {
      onClick: () => {
        backToModal();
        closeMultiStepForm('submitToVendor');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex flexDir="column" className="gap-12">
      <Typography
        color="secondaryDisabledText"
        size={12}
        fontWeight={400}
        className="text-end"
      >
        {checkedIds?.length}/{MAXIMUM_SUBMISSION}
      </Typography>
      <TwoButtonFooter
        submitLabel={t('next')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={!checkedIds?.length}
        onSubmitClick={() => setStep((prev) => prev + 1)}
        secondaryButtonOnClick={() => closeMultiStepForm('submitToVendor')}
      />
    </Flex>
  );

  const handleItemClick = (id: string) => {
    const isCurrentlySelected = checkedIds.includes(id);

    if (isCurrentlySelected) {
      setCheckedIds((prev) => prev.filter((val) => val !== id));
    } else {
      if (checkedIds.length >= +totalCount) return;
      setCheckedIds((prev) => [...prev, id]);
    }
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: () => (
        <SearchableAsyncList<SuggestSubmitToVendor>
          variant="multi"
          name={QueryKeys.getSuggestCompany}
          renderInfoMessage={
            <MenuItem
              className="!bg-gray_5 !my-12"
              title={t('maximum_multi_submissions')}
              iconName="info-circle"
            />
          }
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.croppedImageUrl}
              title={item.title}
              subTitle={item?.username}
              onClick={() => handleItemClick(item?.id)}
            >
              <CheckBox
                value={checkedIds.some((val) => item?.id === val)}
                disabled={excludedJobs?.find((val: any) => item?.id === val)}
              />
            </ItemComponent>
          )}
          pageSize={20}
          params={{
            companyFilter: 'ONLY_APPROVED_VENDOR',
            jobId: submitVendorData?.jobId,
          }}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          keywords="text"
          apiFunc={getSuggestCompany}
          placeholder={t('search_submit_vendor')}
          renderNextPageLoading={<SubmitVendorItemSkeleton />}
          enableInfiniteScroll
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
