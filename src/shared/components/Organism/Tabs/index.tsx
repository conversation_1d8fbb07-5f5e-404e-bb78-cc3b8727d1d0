import {
  type ReactElement,
  type ReactNode,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import Divider from '@shared/uikit/Divider';
import Flex from '@shared/uikit/Flex/index';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './index.module.scss';
import NavLink from './NavLink';
import type useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';

interface StyleProps {
  content?: string;
  linksRoot?: string;
  tabsWrap?: string;
  linksWrap?: string;
  tabsRoot?: string;
}

export type TabType<PT> = {
  title: string;
  path: PT;
  width?: string | number;
  subText?: string | number;
  titleClass?: string;
  center?: boolean;
  content?: (() => ReactElement) | ReactNode;
  icon?: ReactNode;
};

export type TabsProps<PT = string> = {
  tabs: TabType<PT>[] | Readonly<TabType<PT>[]>;
  rightAction?: ReactNode;
  leftAction?: ReactNode;
  isLoading?: boolean;
  activePath?: PT;
  styles?: Partial<StyleProps>;
  visibleTabs?: boolean;
  isFullWidth?: boolean;
  onChangeTab?: (path: PT) => void;
  renderAllTabs?: boolean;
  divide?: boolean;
  className?: string;
  confirmBeforeChange?: boolean;
  autoScrollActiveTab?: boolean;
  openConfirmDialog?: ReturnType<typeof useOpenConfirm>['openConfirmDialog'];
};

function Tabs<PT extends string>({
  styles,
  tabs,
  leftAction,
  rightAction,
  isLoading,
  activePath,
  onChangeTab,
  visibleTabs = true,
  isFullWidth = true,
  renderAllTabs,
  divide,
  className,
  confirmBeforeChange,
  openConfirmDialog,
  autoScrollActiveTab = false, // default off
}: TabsProps<PT>) {
  const { t } = useTranslation();
  const tabsList = tabs.filter(Boolean);
  const firstUpdate = useRef(true);
  const [active, setActive] = useState(activePath || tabs[0]?.path);

  const tabRefs = useRef<Record<string, HTMLButtonElement | null>>({});

  const activeContent = tabs.find((item) => item.path === active)?.content;

  const onSetActiveTab = (path: PT) => {
    if (confirmBeforeChange && openConfirmDialog) {
      openConfirmDialog({
        title: t('confirm_title'),
        message: t('confirm_desc'),
        cancelButtonText: t('confirm_cancel'),
        confirmButtonText: t('confirm_ok'),
        cancelCallback: () => setActive(path),
        isReverse: true,
      });
    } else setActive(path);
  };

  useLayoutEffect(() => {
    if (firstUpdate.current) {
      firstUpdate.current = false;
      return;
    }
    onChangeTab?.(active);
  }, [active]);

  useEffect(() => {
    if (autoScrollActiveTab && active) {
      const el = tabRefs.current[String(active)];
      el?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center',
      });
    }
  }, [active, autoScrollActiveTab]);

  useEffect(() => {
    if (activePath && active !== activePath) {
      setActive(activePath);
    }
  }, [activePath]);

  return (
    <Flex className={cnj(classes.tabsRoot, styles?.tabsRoot, className)}>
      <Flex className={cnj(classes.tabsWrap, styles?.tabsWrap)}>
        <Flex className={cnj(classes.linksRoot, styles?.linksRoot)}>
          <Flex className={cnj(classes.linksWrap, styles?.linksWrap)}>
            {isLoading ? (
              <>
                {[1, 2, 3, 4].map((i) => (
                  <Skeleton key={i} className={classes.skeleton} />
                ))}
              </>
            ) : (
              <>
                {leftAction}
                {visibleTabs &&
                  tabsList.map(
                    (
                      { title, subText, titleClass, path, width, icon, center },
                      index
                    ) => (
                      <NavLink
                        key={path}
                        ref={(el) => {
                          tabRefs.current[String(path)] = el;
                        }}
                        {...{
                          isFullWidth: isFullWidth && tabs?.length > 1,
                          onClick: () => onSetActiveTab(path),
                          isActive: active === path,
                          center,
                          subText,
                          titleClass,
                          title,
                          width,
                          icon,
                          isFirstLink: index === 0,
                        }}
                      />
                    )
                  )}
                {rightAction}
              </>
            )}
          </Flex>
        </Flex>
        {divide && <Divider />}
        {renderAllTabs
          ? tabs?.map((item) => (
              <Flex
                key={item.path}
                className={cnj(
                  classes.content,
                  styles?.content,
                  active !== item.path && classes.hidden
                )}
              >
                {typeof item.content === 'function'
                  ? item.content()
                  : item.content}
              </Flex>
            ))
          : activeContent && (
              <Flex className={cnj(classes.content, styles?.content)}>
                {typeof activeContent === 'function'
                  ? activeContent()
                  : activeContent}
              </Flex>
            )}
      </Flex>
    </Flex>
  );
}

export default Tabs;
