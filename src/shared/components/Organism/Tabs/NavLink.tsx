import React, { forwardRef } from 'react';
import useCssVariables from '@shared/hooks/useCssVariables';
import BaseButton from '@shared/uikit/Button/BaseButton';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import classes from './NavLink.module.scss';

interface NavLinkProps {
  title?: React.ReactNode;
  icon?: React.ReactNode;
  width?: number | string;
  onClick: () => void;
  isActive?: boolean;
  center?: boolean;
  titleClass?: string;
  subText?: string | number;
  isFullWidth?: boolean;
  isFirstLink?: boolean;
}

const NavLink = forwardRef<HTMLButtonElement, NavLinkProps>(
  (
    {
      isActive,
      onClick,
      title,
      width,
      icon,
      center,
      titleClass,
      subText,
      isFullWidth,
      isFirstLink,
    },
    ref
  ) => {
    const styles = useCssVariables({
      scope: classes.container,
      variables: {
        width,
      },
    });

    return (
      <span
        className={cnj(
          classes.container,
          isFullWidth && classes.fullWidthContainer
        )}
      >
        {styles}
        <BaseButton
          className={cnj(
            classes.link,
            isFirstLink && classes.isFirstLink,
            isFullWidth && classes.fullWidthLink,
            isActive && classes.activeLink,
            center && classes.centerItem,
            titleClass && titleClass
          )}
          onClick={onClick}
          ref={ref}
        >
          <span className={classes.NavLinkInnerContent}>
            {icon} {title}
            {subText && (
              <Typography font="400" size={14} color="disabledGrayDark">
                {subText}
              </Typography>
            )}
          </span>
        </BaseButton>
      </span>
    );
  }
);

NavLink.displayName = 'NavLink';

export default NavLink;
