import React, { type FC, useMemo } from 'react';
import { MAIN_CENTER_WRAPPER_ID } from '@shared/constants/enums';
import UnitSectionSkeleton from 'shared/components/molecules/UnitSectionSkeleton/UnitSectionSkeleton';
import PageCard from 'shared/components/Organism/PageCard';
import PeopleCard from 'shared/components/Organism/PeopleCard';
import UnitSectionItem from 'shared/components/Organism/UnitSection/UnitSection.item';
import { useObjectClicks } from 'shared/hooks/useObjectClicks';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import ViewPortList from 'shared/uikit/ViewPortList';
import Section from '../../molecules/Section';
import classes from './InfiniteScrollUnitSection.module.scss';
import type { ObjectSectionContainerProps } from '../../molecules/Section';
import type { ObjectCardProps } from '@shared/components/molecules/ObjectCard';
import type { QueryKeyType } from 'shared/types/general';
import type { AvatarProps } from 'shared/uikit/Avatar';

export interface UnitSectionProps {
  className?: string;
  isPage?: boolean;
  isLoading?: boolean;
  sectionProps?: ObjectSectionContainerProps;
  avatarProps?: AvatarProps;
  list?: Array<ObjectCardProps>;
  emptyState?: React.ReactNode;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  queryKey?: QueryKeyType;
  onSuccess?: (item: any) => void;
  showDefaultCard?: boolean;
  hasNextPage?: boolean;
  cardClickProps?: Record<any, any>;
  component?: (objectElement: any) => React.ReactNode;
}

const InfiniteScrollUnitSection: FC<UnitSectionProps> = ({
  className,
  isPage,
  isLoading,
  sectionProps,
  avatarProps: aP,
  list = [],
  emptyState = null,
  isFetchingNextPage,
  fetchNextPage,
  queryKey,
  onSuccess,
  showDefaultCard,
  hasNextPage,
  cardClickProps,
  component,
}) => {
  const { handleTagClick } = useObjectClicks();

  const handleClick = (objectElement: any) => () => {
    handleTagClick(
      objectElement.username,
      objectElement.id,
      aP?.isCompany ? 'pages' : 'people',
      cardClickProps
    );
  };

  const _data = useMemo<ObjectCardProps[][]>(
    () =>
      (list
        ?.map((item, index) =>
          index % 3 === 0 ? [item, list[index + 1], list[index + 2]] : false
        )
        ?.filter((item) => item) || []) as ObjectCardProps[][],
    [list]
  );

  const renderItem = (objectElement: any) => {
    if (component) {
      return (
        <BaseButton
          onClick={handleClick(objectElement)}
          className={cnj(classes.itemClassName)}
        >
          {component(objectElement)}
        </BaseButton>
      );
    }
    if (showDefaultCard)
      return (
        <BaseButton
          onClick={handleClick(objectElement)}
          className={cnj(classes.itemClassName)}
        >
          <UnitSectionItem item={objectElement} avatarProps={aP} />
        </BaseButton>
      );

    return aP?.isCompany ? (
      <BaseButton
        onClick={handleClick(objectElement)}
        className={cnj(classes.itemClassName)}
      >
        <PageCard
          page={objectElement}
          queryKey={queryKey}
          onSuccess={onSuccess}
        />
      </BaseButton>
    ) : (
      <BaseButton
        onClick={handleClick(objectElement)}
        className={cnj(classes.itemClassName)}
      >
        <PeopleCard
          people={objectElement}
          queryKey={queryKey}
          onSuccess={onSuccess}
        />
      </BaseButton>
    );
  };

  const itemContent = (index: number) => {
    const item1 = _data[index][0];
    const item2 = _data[index][1];
    const item3 = _data[index][2];

    return (
      <Flex className={classes.setOfItems}>
        {!!item1 && renderItem(item1)}
        {!!item2 && renderItem(item2)}
        {!!item3 && renderItem(item3)}
      </Flex>
    );
  };

  const endReached = () =>
    isLoading || isFetchingNextPage || !hasNextPage
      ? undefined
      : fetchNextPage?.();

  const isEmpty = !list?.length && !isLoading;

  if (isEmpty) {
    return emptyState;
  }

  return (
    <Section className={cnj(classes.section, className)} {...sectionProps}>
      {isLoading ? (
        <UnitSectionSkeleton
          totalElements={isFetchingNextPage ? 3 : 6}
          isPage={isPage}
        />
      ) : (
        <ViewPortList
          totalCount={_data.length}
          itemContent={itemContent}
          className={classes.virtualContainer}
          useRelativeScroller
          endReached={endReached}
          customScrollParent={
            document.getElementById(MAIN_CENTER_WRAPPER_ID) || undefined
          }
        />
      )}
      {isFetchingNextPage && (
        <UnitSectionSkeleton totalElements={3} isPage={isPage} />
      )}
    </Section>
  );
};

export default InfiniteScrollUnitSection;
