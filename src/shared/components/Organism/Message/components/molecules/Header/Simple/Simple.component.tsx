import React from 'react';
import Avatar from 'shared/uikit/Avatar';
import Flex from 'shared/uikit/Flex';
import ModalHeaderBase from 'shared/uikit/Modal/BasicModal/Headers/Base';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import classes from './Simple.component.module.scss';
import type { IChatRoom } from 'shared/types/messaging';
import type { AvatarProps } from 'shared/uikit/Avatar';
import type { TypographyProps } from 'shared/uikit/Typography';

export interface ChatHeaderSimpleProps extends ChatHeaderBaseProps {
  title?: string;
  titleProps?: Omit<TypographyProps, 'children'>;
  subTitle?: string;
  subTitleProps?: Omit<TypographyProps, 'children'>;
  avatarProps?: AvatarProps;
  visibleAvatar?: boolean;
  onClickAvatar?: () => void;
  onClickHeader?: () => void;
  belowActions: any;
  activeRoom: IChatRoom;
}

const ChatHeaderSimple: React.FC<ChatHeaderSimpleProps> = (props) => {
  const {
    title,
    titleProps = {},
    subTitle,
    subTitleProps = {},
    avatarProps = {},
    visibleAvatar,
    onClickAvatar,
    onClickHeader,
    belowActions,
    activeRoom,
    ...rest
  } = props;

  return (
    <ModalHeaderBase {...rest} belowContent={belowActions} noCloseButton>
      <Flex flexDir="row" className={classes.fullWidth}>
        <Flex onClick={onClickAvatar} className={cnj(classes.leftContainer)}>
          {visibleAvatar && (
            <Avatar
              defaultTag
              className={classes.avatar}
              size="smd"
              name={activeRoom?.name}
              {...avatarProps}
            />
          )}
          <Flex className={classes.titleWrapper}>
            <Typography
              onClick={onClickHeader}
              lineNumber={1}
              font="700"
              height={19}
              size={16}
              color="smoke_coal"
              className={classes.headerTitle}
              {...titleProps}
            >
              {title}
            </Typography>
            {subTitle && (
              <Typography
                size={14}
                height={16}
                color="secondaryDisabledText"
                className={classes.subtitle}
                {...subTitleProps}
              >
                {subTitle}
              </Typography>
            )}
          </Flex>
        </Flex>
        <Flex className={classes.filler} />
        {rest?.rightActions}
      </Flex>
    </ModalHeaderBase>
  );
};

export default ChatHeaderSimple;
