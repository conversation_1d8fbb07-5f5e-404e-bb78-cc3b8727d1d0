import React, { useMemo } from 'react';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import UnitSectionSkeleton from 'shared/components/molecules/UnitSectionSkeleton/UnitSectionSkeleton';
import SearchSection from 'shared/components/Organism/SearchSection';
import Button from 'shared/uikit/Button';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import ViewPortList from 'shared/uikit/ViewPortList';
import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './index.module.scss';

interface SearchAllSectionProps {
  isPages?: boolean;
  routeName: (item: any) => string;
  showAllRouteName: string;
  scopes: Array<string>;
  title: string;
  renderItem: (item: any, index: number) => React.ReactElement;
  itemClassName?: string;
  isLoading: boolean;
  totalElements?: number;
  data: Array<any>;
  listWrapperClassName?: string;
  visibleShowAll: boolean;
  onSelectHandler?: (item: any) => void;
  emptyComponent?: () => React.ReactElement;
  useVirtual?: boolean;
  visibleShowAllBeneathButton?: boolean;
}

const SearchAllSection: React.FC<SearchAllSectionProps> = ({
  isPages,
  isLoading,
  showAllRouteName,
  scopes,
  title,
  renderItem,
  itemClassName,
  totalElements,
  visibleShowAll = true,
  data,
  listWrapperClassName,
  onSelectHandler,
  emptyComponent,
  useVirtual,
  visibleShowAllBeneathButton,
}): JSX.Element => {
  const { t } = useTranslation();
  const history = useHistory();

  const _data = useMemo(
    () =>
      data
        ?.map((item, index) =>
          index % 3 === 0 ? [item, data[index + 1], data[index + 2]] : false
        )
        ?.filter(Boolean),
    [data]
  );

  if (!data?.length && !isLoading && !emptyComponent) {
    return null;
  }

  const onClickItem = (item) => () => {
    onSelectHandler?.(item);
  };

  const itemContent = (index: number) => {
    const item1 = _data[index][0];
    const item2 = _data[index][1];
    const item3 = _data[index][2];

    return (
      <Flex className={classes.setOfItems}>
        {!!item1 && (
          <BaseButton
            onClick={onClickItem(item1)}
            className={cnj(classes.itemClassName, itemClassName)}
            key={`${item1.id}_${item1.title}`}
          >
            {renderItem(item1)}
          </BaseButton>
        )}
        {!!item2 && (
          <BaseButton
            onClick={onClickItem(item2)}
            className={cnj(classes.itemClassName, itemClassName)}
            key={`${item2.id}_${item2.title}`}
          >
            {renderItem(item2)}
          </BaseButton>
        )}
        {!!item3 && (
          <BaseButton
            onClick={onClickItem(item3)}
            className={cnj(classes.itemClassName, itemClassName)}
            key={`${item3.id}_${item3.title}`}
          >
            {renderItem(item3)}
          </BaseButton>
        )}
      </Flex>
    );
  };

  const handleShowAllClick = () => {
    history.push(showAllRouteName);
  };

  return (
    <PermissionsGate scopes={scopes}>
      <SearchSection
        title={title}
        resultsCaption={t('results')}
        resultsNumber={`${totalElements}`}
        visibleShowAll={visibleShowAll}
        routeName={showAllRouteName}
        className={classes.listWrapper}
      >
        {isLoading ? (
          <UnitSectionSkeleton totalElements={6} isPage={isPages} />
        ) : !data.length ? (
          emptyComponent()
        ) : (
          <Flex className={cnj(classes.listWrapper, listWrapperClassName)}>
            {useVirtual ? (
              <ViewPortList
                totalCount={_data.length}
                itemContent={itemContent}
                className={classes.virtualContainer}
                useRelativeScroller
              />
            ) : (
              <>
                {data.map((item, index: number) => (
                  <BaseButton
                    onClick={onClickItem(item)}
                    className={cnj(classes.itemClassName, itemClassName)}
                    key={`${item.id}_${item.title}`}
                  >
                    {renderItem(item, index)}
                  </BaseButton>
                ))}
              </>
            )}
            {visibleShowAllBeneathButton && (
              <Button
                rightIcon="chevron-right"
                labelFont="bold"
                schema="semi-transparent"
                label={t('see_all')}
                onClick={handleShowAllClick}
                className={classes.seeAllBeneathButton}
              />
            )}
          </Flex>
        )}
      </SearchSection>
    </PermissionsGate>
  );
};

export default SearchAllSection;
