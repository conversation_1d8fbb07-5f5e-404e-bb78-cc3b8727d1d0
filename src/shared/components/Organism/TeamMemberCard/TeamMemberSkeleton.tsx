import React from 'react';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import cnj from '@shared/uikit/utils/cnj';
import classes from '@shared/components/molecules/BusinessJobCard/BusinessJobCard.module.scss';
import canClasses from '@shared/components/molecules/CandidateCard/CandidateCard.module.scss';

export default function TeamMemberSkeleton() {
  return (
    <CardWrapper
      classNames={{
        root: cnj(
          canClasses.root,
          '!border !border-solid !border-techGray_20 !rounded-xl'
        ),
        container: cnj(classes.container, canClasses.container),
      }}
    >
      <Flex flexDir="row" className="!h-[86px]">
        <Skeleton className="rounded-full !size-[80px]" />
        <Flex className="gap-4 flex-1 ml-4">
          <Skeleton className="h-16 rounded" />
          <Skeleton className="h-16 rounded" />
          <Skeleton className="h-16 rounded" />
          <Skeleton className="h-16 rounded" />
        </Flex>
      </Flex>

      <Flex className={classes.badges}>
        <Skeleton className="!w-[48px] h-26 rounded" />
        <Skeleton className="!w-[48px] h-26 rounded" />
        <Skeleton className="!w-[48px] h-26 rounded" />
        <Skeleton className="!w-[48px] h-26 rounded" />
        <Skeleton className="!w-[48px] h-26 rounded" />
        <Skeleton className="!w-14 h-16 rounded ml-auto" />
      </Flex>

      <Flex className="!flex-row gap-12">
        <Skeleton className="flex-1 h-40 rounded" />
        <Skeleton className="flex-1 h-40 rounded" />
      </Flex>
    </CardWrapper>
  );
}
