@import '/src/shared/theme/theme.scss';

@layer organism {
  .submitWrap {
    flex-direction: row;
    padding-top: variables(gutter);
    border-top: 1px solid colors(techGray_10);
    margin-top: auto;
  }
  .btn {
    flex: 1;
  }
  .modalBody {
    overflow-y: unset;
    padding: variables(gutter);
  }
  .divider {
    min-width: variables(gutter);
    max-width: variables(gutter);
  }
  .form {
    height: 100%;
    overflow-y: auto;
  }
  .header {
    z-index: 504;
    border-bottom: 1px solid colors(techGray_10);
  }
  .groupHeaderSkeleton {
    margin-bottom: variables(gutter) * 0.5;
    border-radius: variables(gutter) * 0.25;
    flex: 0 0 auto;
    width: 33%;
    height: 18px;
  }
  .wrapper {
    gap: variables(gutter) * 0.5;
  }
  .itemSkeleton {
    width: 100%;
    height: 40px;
    border-radius: variables(gutter) * 0.25;
  }
  .skeletonDivider {
    margin: variables(gutter) 0;
  }
  .buttonSkeleton {
    flex: 1 1 0;
    height: 32px;
    border-radius: variables(gutter) * 0.25;
  }
  .skeletonModalWrapper {
    overflow: hidden;
  }
  .skeletonModalFooter {
    flex-direction: row;
  }
  @media (min-width: breakpoints(tablet)) {
    .submitWrap {
      padding: variables(gutter);
    }
  }
  .formGroupItemFullWidth {
    display: block;
    column-count: 4;
    column-gap: variables(gutter);
    width: 100%;
    min-height: 100vh;
    & .formItem {
      break-inside: avoid;
      margin: 0 0 variables(gutter);
      background-color: colors(gray_5);
      border-radius: 12px;
      padding: 12px;
    }
  }

  .formItemFullWidthFirst {
    & .formItem:first-child {
      background-color: unset;
      padding: 0;
      width: 100%;
      column-span: all;
    }
  }
}
.formGroupItem {
  & .formItem {
    border-radius: 12px;
    padding: 12px;
  }
}
