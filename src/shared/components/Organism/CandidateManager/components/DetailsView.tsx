import React, { useEffect, useCallback, useRef, useState } from 'react';
import classes from '@app/search/candidates/partials/CandidateDetails.module.scss';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import CandidateCard, {
  CandidateCardActions,
  CandidateCardSkeleton,
} from '@shared/components/molecules/CandidateCard';
import CardBadge from '@shared/components/molecules/CardBadge';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import Button from '@shared/uikit/Button';
import IconButton from '@shared/uikit/Button/IconButton';
import DateView from '@shared/uikit/DateView';
import EmbededView from '@shared/uikit/EmbededView';
import Flex from '@shared/uikit/Flex';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import { RichTextView } from '@shared/uikit/RichText';
import Tooltip from '@shared/uikit/Tooltip';
import { editCandidateAdditionalInfo } from '@shared/utils/api/candidates';
import { getEmbededDocumentLink } from '@shared/utils/getEmbededDocumentLink';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { useManagerContext } from '../CandidateManager.context';
import CandidateVsJobComparison from './CandidateVsJobComparison';
import UploadCandidateResume from './FiltersBody/UploadCandidateResume';
import type { CandidateFormData } from '@shared/types/candidates';
import type { ValueLabelType } from '@shared/types/general';
import type { FC } from 'react';

interface Props {
  className?: string;
}

export const CandidateManagerDetailsView: FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const resumeUploadBoxRef = useRef<any>(null);
  const { candidate, selectedSummary, setSelectedTab, setCandidate } =
    useManagerContext();
  const resumesList = [
    !!candidate?.candidateResumeUrl && {
      value: candidate?.candidateResumeUrl,
      label: t('candidates_resume'),
    },
    !!candidate?.recruiterResumeUrl && {
      value: candidate?.recruiterResumeUrl,
      label: t('recruiters_resume'),
    },
    !!candidate?.socialResumeUrl && {
      value: candidate?.socialResumeUrl,
      label: t('social_candidates_resume'),
    },
  ].filter(Boolean);
  const [selectedResume, setSelectedResume] = useState<ValueLabelType>(
    resumesList?.[0] || {}
  );
  const { embededSrc } = getEmbededDocumentLink(selectedResume?.value ?? '');
  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();
  useEffect(() => {
    if (resumesList.length && !selectedResume.value) {
      setSelectedResume(resumesList[0]);
    }
  }, [resumesList]);

  useEffect(() => {
    if (selectedResume?.value) {
      setSelectedResume({});
    }
  }, [candidate?.id]);

  const handleReplaceClick = (e) => {
    resumeUploadBoxRef.current?.openPicker(e);
  };
  const onSuccess = useCallback(
    (data: CandidateFormData) => {
      setCandidate((prevCandidate) =>
        prevCandidate
          ? {
              ...prevCandidate,
              tags: data.tags,
            }
          : prevCandidate
      );
    },
    [setCandidate]
  );

  return (
    <Flex className={className}>
      {candidate ? (
        <CandidateCard
          avatar={candidate?.profile?.croppedImageUrl}
          firstText={candidate?.profile?.fullName}
          secondText={candidate?.profile?.usernameAtSign}
          thirdText={candidate.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(
            candidate?.profile?.location?.title || ''
          )}
          FirstTextWrapper={
            !candidate.profile.username ? IsManualWrapper : undefined
          }
          treeDotMenu={<CandidateCardActions candidate={candidate} />}
        >
          <HorizontalTagList
            tags={candidate.tags}
            title={t('candidate_tags')}
            editable
            onSuccess={onSuccess}
            apiFunc={(body) =>
              editCandidateAdditionalInfo({ candidateId: candidate.id, body })
            }
          />
          <Flex className={classes.badges}>
            <CardBadge
              value={candidate.notesCount}
              iconsDetails={{ iconName: 'note' }}
              tooltipProps={{
                children: t('notes'),
              }}
              onClick={() => setSelectedTab('notes')}
            />
            <CardBadge
              value={candidate.todosCount}
              iconsDetails={{ iconName: 'checklist' }}
              tooltipProps={{
                children: t('todos'),
              }}
              onClick={() => setSelectedTab('todos')}
            />
            <CardBadge
              value={candidate.meetingsCount}
              iconsDetails={{ iconName: 'meeting' }}
              tooltipProps={{
                children: t('meetings'),
              }}
              onClick={() => setSelectedTab('meetings')}
            />
            {!!candidate.lastModifiedDate && (
              <Flex className="ml-auto">
                <Tooltip
                  trigger={
                    <DateView
                      className={classes.counterDate}
                      value={`${candidate.lastModifiedDate}`}
                    />
                  }
                  placement="top"
                  className="!bg-hoverPrimary"
                  arrowClassName="before:!bg-hoverPrimary"
                  hidden={!candidate?.lastActivity?.id}
                >
                  {candidate?.lastActivity?.id && (
                    <Flex className="p-6">
                      <ActivityItem item={candidate?.lastActivity} />
                    </Flex>
                  )}
                </Tooltip>
              </Flex>
            )}
          </Flex>
        </CandidateCard>
      ) : (
        <CandidateCardSkeleton showBadges showTags />
      )}
      {selectedSummary?.type !== ORIGINAL_CANDIDATE && (
        <CandidateVsJobComparison />
      )}
      {candidate?.coverLetter ? (
        <SectionLayout title={t('cover_letter')}>
          <RichTextView
            html={candidate.coverLetter}
            typographyProps={{
              size: 15,
              color: 'thirdText',
              height: 16,
            }}
            showMore
            onMentionClick={handleTagClick}
            onHashtagClick={handleHashtagClick}
            onHashtagHover={onHashtagHover}
            hoveredHashtag={hoveredHashtag}
          />
        </SectionLayout>
      ) : null}
      <SectionLayout
        title={t('resume')}
        classNames={{ childrenWrap: 'min-h-[100px]' }}
        visibleActionButton={resumesList.length > 0}
        actionButton={
          resumesList.length > 0 && (
            <Flex flexDir="row" className="gap-4">
              <PopperMenu
                placement="bottom-start"
                closeOnScroll
                buttonComponent={(visible: boolean) => (
                  <Button
                    label={selectedResume?.label}
                    rightSvg={
                      <IconButton
                        className="ml-8"
                        size="tiny"
                        colorSchema="secondary-transparent"
                        noHover
                        name={visible ? 'chevron-up' : 'chevron-down'}
                      />
                    }
                    leftType="far"
                    schema="transparent"
                    className="border border-solid border-techGray_20 !py-8 !px-12"
                    labelProps={{ font: '700', size: 15, color: 'primaryText' }}
                  />
                )}
              >
                <Flex className="w-full sm:w-[224px]">
                  {resumesList.map(({ value, label }, index) => (
                    <PopperItem
                      key={`${value}_${index}`}
                      onClick={() => setSelectedResume(value)}
                      // iconName={icon}
                      iconType="far"
                      label={label}
                    />
                  ))}
                </Flex>
              </PopperMenu>
              <Tooltip
                trigger={
                  <IconButton
                    colorSchema="tertiary-transparent"
                    type="far"
                    name="replace"
                    size="md15"
                    onClick={handleReplaceClick}
                  />
                }
              >
                {t('replace')}
              </Tooltip>
              {/* <Tooltip */}
              {/*  trigger={ */}
              {/*    <IconButton */}
              {/*      colorSchema="transParentError" */}
              {/*      type="far" */}
              {/*      name="trash" */}
              {/*      size="md15" */}
              {/*    /> */}
              {/*  } */}
              {/* > */}
              {/*  {t('delete')} */}
              {/* </Tooltip> */}
              <Tooltip
                trigger={
                  <IconButton
                    href={embededSrc}
                    disabled={!embededSrc}
                    colorSchema="secondary-transparent"
                    type="far"
                    name="eye"
                    size="md15"
                  />
                }
              >
                {t('view_as_pdf')}
              </Tooltip>
            </Flex>
          )
        }
      >
        {selectedResume?.value ? (
          <EmbededView src={selectedResume.value} className="rounded" />
        ) : candidate ? (
          <UploadCandidateResume
            ref={resumeUploadBoxRef}
            candidate={candidate}
          />
        ) : null}
      </SectionLayout>
      <div className="mb-5" />
    </Flex>
  );
};
