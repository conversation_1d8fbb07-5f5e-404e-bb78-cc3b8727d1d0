import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import CandidateManagerLayout from './CandidateManager.layout';
import CandidateManagerProvider from './CandidateManager.provider';
import type { FC } from 'react';

interface Props {
  participationId: string;
}

const CandidateManagerModal: FC<Props> = () => {
  const candidateManager = useGlobalState('candidateManager');
  const appDispatch = useGlobalDispatch();
  const { tab } = candidateManager;
  const onClose = () => {
    appDispatch({
      type: 'TOGGLE_CANDIDATE_MANAGER',
      payload: { isOpen: false, breifScoreSection: true },
    });
  };

  return (
    <FixedRightSideModal
      wide
      doubleColumn
      onBack={onClose}
      onClose={onClose}
      isOpenAnimation
      contentClassName="!max-w-full !bg-tooltipText overflow-hidden !border-l-0"
    >
      <ModalBody className="overflow-hidden">
        <CandidateManagerProvider selectedTab={tab}>
          <CandidateManagerLayout />
        </CandidateManagerProvider>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default CandidateManagerModal;
