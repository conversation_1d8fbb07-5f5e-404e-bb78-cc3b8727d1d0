import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { QueryKeys } from '@shared/utils/constants';
import useAcceptMemberShip from 'shared/hooks/api-hook/useAcceptMemberShip';
import useDeclineMemberShip from 'shared/hooks/api-hook/useDeclineMemberShip';
import { APP_ENTITIES } from 'shared/utils/constants/app-entities';
import { ToggleNotificationList } from 'shared/utils/constants/NotificationVariants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import Layout, { Avatar, Alert } from '../Notification.layout';
import { renderNotificationText } from '../utils/renderNotificationText';
import type { INotificationProps } from '../Notification.layout';

type Props = {
  isAcceptedAlready?: boolean;
  isDeclinedAlready?: boolean;
};
const NewPageRoleAssigned: React.FC<INotificationProps<Props>> = ({
  data,
  onSeen,
  menuActions,
  updateNotification,
  decreaseBadgeCount,
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [result, setResult] = useState<any>(
    data.isAcceptedAlready
      ? { text: `${t('you_accepted_role')}.` }
      : data.isDeclinedAlready
        ? { text: `${t('you_declined_role')}.`, variant: 'error' }
        : undefined
  );

  const { mutate: acceptMemberShip } = useAcceptMemberShip();
  const { mutate: declineMemberShip } = useDeclineMemberShip();

  const handleAcceptMemberShip = () => {
    onSeen?.();
    acceptMemberShip(
      { role: data.pageRole, pageId: data.pageId },
      {
        onSuccess: () => {
          setResult({ text: `${t('you_accepted_role')}.` });
          updateNotification?.({ isAcceptedAlready: true, seen: true });
          decreaseBadgeCount?.();
          queryClient.invalidateQueries({
            queryKey: [QueryKeys.getFullAccessibilityGuide],
            exact: false,
          });
        },
      }
    );
  };
  const handleDeclineMemberShip = () => {
    onSeen?.();
    declineMemberShip(
      { role: data.pageRole, pageId: data.pageId },
      {
        onSuccess: () => {
          setResult({ text: `${t('you_declined_role')}.`, variant: 'error' });
          updateNotification?.({ isDeclinedAlready: true, seen: true });
          decreaseBadgeCount?.();
        },
      }
    );
  };
  const hasToggleNotification = ToggleNotificationList.includes(data.type);
  const handleView = () => {
    onSeen?.();
  };
  const isAdmin = data?.pageRole === 'ADMIN';

  return (
    <Layout
      hasToggleNotification={hasToggleNotification}
      menuActions={menuActions}
      onClick={handleView}
      icon={
        <Avatar
          objectId={data.userId}
          isCompany={data?.userType === APP_ENTITIES.page}
          src={data?.croppedImageUrl}
        />
      }
      description={renderNotificationText(
        translateReplacer(
          isAdmin
            ? t('name_gave_y_role_page')
            : t('person_requested_you_to_be_pageRole_of_pageName_page'),
          [
            data?.userTitle,
            isAdmin
              ? t(data?.pageRole?.toLowerCase())
              : t(data.pageRole?.toLowerCase()),
            data?.pageTitle,
          ]
        ),
        [
          data?.userTitle,
          isAdmin
            ? t(data?.pageRole?.toLowerCase())
            : t(data.pageRole?.toLowerCase()),
          data?.pageTitle,
        ]
      )}
      moreContent={
        result ? (
          <Alert text={result.text} variant={result.variant} />
        ) : undefined
      }
      primaryAction={
        !result &&
        !isAdmin && {
          label: t('accept'),
          onClick: handleAcceptMemberShip,
        }
      }
      secondaryAction={
        !result &&
        !isAdmin && {
          label: t('decline'),
          onClick: handleDeclineMemberShip,
        }
      }
      date={data?.createdDate}
      seen={data?.seen}
    />
  );
};

export default NewPageRoleAssigned;
