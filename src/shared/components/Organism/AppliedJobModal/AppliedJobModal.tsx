import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';
import ModalHeaderSearch from '@shared/uikit/Modal/BasicModal/components/ModalSearch';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';

const AppliedJobModal = () => {
  const { t } = useTranslation();

  const handleClose = () => {
    closeMultiStepForm('appliedJobModal');
  };

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('applied_jobs')} />
      <ModalHeaderSearch />
      <ModalBody className="!p-0"></ModalBody>
    </FixedRightSideModal>
  );
};

export default AppliedJobModal;
