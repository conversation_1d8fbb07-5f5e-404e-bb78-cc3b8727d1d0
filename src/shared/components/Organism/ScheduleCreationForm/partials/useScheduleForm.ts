import { useSearchParams } from 'next/navigation';
import { useGetAllProviders } from '@shared/components/molecules/EventsIntegration/utils/useGetAllProviders';
import { OWNER_ENTITY_TYPE } from '@shared/components/Organism/ScheduleCreationForm/constants';
import { useDefaultTemplate } from '@shared/contexts/DefaultTemplate/defaultTemplate.provider';
import useGetPrivateFileDetails from '@shared/hooks/api-hook/useGetPrivateFileDetails';
import useMeetingTemplates from '@shared/hooks/schedules/useMeetingTemplates';
import useResponseToast from '@shared/hooks/useResponseToast';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { useUpcomingAndPastEvents } from '@shared/hooks/useUpcomingAndPastEvents';
import { createCandidateMeeting } from '@shared/utils/api/candidates';
import { editCandidateMeeting } from '@shared/utils/api/candidates';
import { routeNames } from '@shared/utils/constants';
import { linkValidationNonNullable } from '@shared/utils/form/formValidator/customValidations/linkValidation';
import { getCurrentUrl } from '@shared/utils/getCurrentUrl';
import useUpdateInfinityData from '@shared/utils/hooks/useUpdateInfinityData';
import { getUrlPathname } from '@shared/utils/toolkit/getUrlPathname';
import {
  ConferenceProviderName,
  ProviderType,
} from 'shared/components/molecules/EventsIntegration/utils/type';
import { meetingCreatorAttendeePermissions } from 'shared/constants/schedules';
import useGetCurrentTimeZone from 'shared/hooks/api-hook/useGetCurrentTimeZone';
import { useScheduleFormFieldOptions } from 'shared/hooks/schedules/useScheduleFormFieldOptions';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useSchedulesEvent from 'shared/hooks/useSchedulesEvent';
import { useSetEvents } from 'shared/hooks/useSetEvents';
import { MeetingChannel, TaskStatus } from 'shared/types/schedules/schedules';
import schedulesApi from 'shared/utils/api/schedules';
import { schedulesDb, schedulesEventTypes } from 'shared/utils/constants/enums';
import formValidator, {
  endDateAfterStartCB,
  isEmptyValidator,
  timeValidator,
  noTimeWithoutDate,
} from 'shared/utils/form/formValidator';
import getSchedulesSectionsQueryKey from 'shared/utils/getSchedulesSectionsQueryKey';
import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  meetingDurations,
  meetingReminders,
} from 'shared/utils/normalizers/schedules';
import { BE_TIME_FORMAT, dayjs, Time } from 'shared/utils/Time';
import isUrlMatch from 'shared/utils/urlMatch';
import type {
  BEMeetingDetails,
  BEReminderDetails,
  BETaskDetails,
  CreatableSchedulesEventTypes,
  ICreateMeetingData,
  MeetingDetails,
} from 'shared/types/schedules/schedules';
import type { UploadedFile } from 'shared/uikit/AttachmentPicker/AttachmentPicker.component';

const useScheduleForm = <T extends CreatableSchedulesEventTypes>() => {
  const { handleSuccess } = useResponseToast();
  const {
    schedulesEventType,
    event,
    isCreationMode,
    backHandler,
    closeHandler,
    queryResult,
    creationInitialData,
  } = useSchedulesEvent<T>();
  const currentUrl = getCurrentUrl();
  const currentPathname = getUrlPathname(currentUrl);
  const isInScheduleCalendarTab = isUrlMatch(
    currentPathname,
    routeNames.schedulesCalendar,
    false
  );
  const isInScheduleMeetingTab = isUrlMatch(
    currentPathname,
    routeNames.schedulesMeetings,
    false
  );

  const { state } = useSchedulesUrlState();
  const { refetch: refetchUpcomingAndPastEvents } = useUpcomingAndPastEvents();
  const {
    scheduleEventsPanelData: {
      candidateId,
      participationId,
      isCandidateMode: panelIsCandidateMode,
      isInCandidateManager,
      activeTab,
    } = {},
  } = state || {};
  const { t } = useTranslation();
  const { data: currentTimeZone = {} as any } = useGetCurrentTimeZone();
  const searchParams = useSearchParams();
  const isCandidateMode = searchParams?.get('isCandidateMode') === 'true';

  const { meetingQueryKey, upComingQueryKey, pastQueryKey } =
    getSchedulesSectionsQueryKey(
      schedulesEventType,
      isCandidateMode ? candidateId : participationId,
      activeTab
    );

  const { refetch: refetchMeetings } = useUpdateInfinityData(meetingQueryKey);
  const { refetch: refetchUpcoming } = useUpdateInfinityData(upComingQueryKey);
  const { refetch: refetchPast } = useUpdateInfinityData(pastQueryKey);

  const { fetchEvents } = useSetEvents();
  const { authUser } = useGetAppObject();

  const {
    assigneePermissionOption,
    taskStatusOptions,
    attendeePermissionsOptions,
  } = useScheduleFormFieldOptions();

  const defaultTimeZone = {
    ...currentTimeZone,
    value: currentTimeZone?.id,
  };

  const { getDefaultTemplate } = useDefaultTemplate();
  const { templates } = useMeetingTemplates();
  const defaultTemplate = getDefaultTemplate(templates);

  const initialValues = {
    [schedulesEventTypes.MEETING]: {
      timezone: defaultTimeZone,
      contactType: schedulesDb.contactType[0],
      meetingChannel: MeetingChannel.LOBOX_ACCOUNT,
      attendeePermissions: [],
      permissions: { ...meetingCreatorAttendeePermissions },
      attachmentFileIds: [],
      attendees: [],
      duration: meetingDurations._30_MINUTES,
      remind: meetingReminders._15_MIN_BEFORE,
      creator: authUser,
      title: creationInitialData?.title || defaultTemplate?.title,
      currentUserIsCreator: true,
      description: defaultTemplate?.message || '',
      ...creationInitialData,
    } as Partial<MeetingDetails>,
    [schedulesEventTypes.REMINDER]: { ...creationInitialData },
    [schedulesEventTypes.TASK]: {
      status: {
        label: t('open'),
        value: TaskStatus.OPEN,
      },
      ...creationInitialData,
    },
  };

  const { data: attachmentFiles = [], refetch: refetchAttachmentFiles } =
    useGetPrivateFileDetails(event?.attachmentFileIds, ['event', event?.id], {
      enabled: !!event?.attachmentFileIds?.length,
    });

  const { data: externalConferenceProvider, refetch: refetchProviders } =
    useGetAllProviders(ProviderType.Conference, {
      enabled:
        event &&
        'externalConferenceProviderType' in event &&
        !!event.externalConferenceProviderType &&
        event.externalConferenceProviderType !== 'LOBOX' &&
        !!event.externalConferenceProviderId,
      select(data) {
        if (isCreationMode) return undefined;
        if (
          event &&
          'externalConferenceProviderType' in event &&
          !!event.externalConferenceProviderType
        ) {
          const currentProviderItem = data?.[
            event.externalConferenceProviderType
          ]?.find(
            ({ id }) => id === String(event.externalConferenceProviderId)
          );
          if (currentProviderItem)
            return {
              label: currentProviderItem?.externalUserName || '',
              value: currentProviderItem,
            };
        }

        return undefined;
      },
    });

  const editInitialValues = event && {
    ...event,
    assigneePermission:
      'assigneePermission' in event && event.assigneePermission
        ? assigneePermissionOption
        : [],
    attendeePermissions:
      'attendees' in event
        ? attendeePermissionsOptions.filter((permissionOption) =>
            event.attendees?.[0]?.permissions.includes(permissionOption.value)
          )
        : [],
    assignee: 'assignee' in event ? event.assignee.user : undefined,
    status:
      'status' in event &&
      taskStatusOptions.find((option) => option.value === event.status),
    startTime: Time.getFormTime(
      Time.getLocalDateFromUTCStr(
        event?.start,
        'timezone' in event ? (event.timezone?.code ?? undefined) : undefined
      )
    ),
    startDate: Time.getFormDate(
      Time.getLocalDateFromUTCStr(
        event?.start,
        'timezone' in event ? (event.timezone?.code ?? undefined) : undefined
      )
    ),
    endTime:
      'end' in event
        ? Time.getFormTime(Time.getLocalDateFromUTCStr(event?.end))
        : undefined,
    endDate:
      'end' in event
        ? Time.getFormDate(Time.getLocalDateFromUTCStr(event?.end))
        : undefined,
    attachmentFiles,
    externalConferenceProvider,
  };

  const meetingParamOptions = {
    createParams: {
      isInCandidateManagerModal: {
        id: isCandidateMode ? candidateId : participationId,
        isCandidateMode,
      },
      isInSchedules: {
        candidateMode: { id: candidateId, isCandidateMode: true },
        participationMode: { id: participationId, isCandidateMode: false },
      },
    },
    updateParams: {
      candidateMode: (data: ICreateMeetingData) => ({
        id: data.id,
        isCandidateMode: true,
      }),
      participationMode: (data: ICreateMeetingData) => ({
        id: data.id,
        isCandidateMode: false,
      }),
    },
  };

  const updateMeetingParams = (data: ICreateMeetingData) => {
    const { ownerEntityType } = data;
    if (ownerEntityType === OWNER_ENTITY_TYPE.CANDIDATE) {
      return meetingParamOptions.updateParams.candidateMode(data);
    }

    if (ownerEntityType === OWNER_ENTITY_TYPE.PARTICIPATION) {
      return meetingParamOptions.updateParams.participationMode(data);
    }

    return null;
  };

  const createMeetingParams = () => {
    if (isInCandidateManager) {
      return meetingParamOptions.createParams.isInCandidateManagerModal;
    }

    if (panelIsCandidateMode && candidateId) {
      return meetingParamOptions.createParams.isInSchedules.candidateMode;
    }

    if (participationId) {
      return meetingParamOptions.createParams.isInSchedules.participationMode;
    }

    return null;
  };

  const getCandidateMeetingParams = (data?: ICreateMeetingData) => {
    return data?.id ? updateMeetingParams(data) : createMeetingParams();
  };

  const createMeetingFunc = (data: ICreateMeetingData) => {
    const params = getCandidateMeetingParams();

    return params?.id
      ? createCandidateMeeting(params.id, data, params.isCandidateMode)
      : schedulesApi.createMeeting(data);
  };

  const updateMeetingFunc = (data: ICreateMeetingData) => {
    const params = getCandidateMeetingParams(data);

    return params?.id
      ? editCandidateMeeting(params.id, data, params.isCandidateMode)
      : schedulesApi.updateMeeting(data);
  };

  const apiFunc = {
    [schedulesEventTypes.MEETING]: isCreationMode
      ? createMeetingFunc
      : updateMeetingFunc,
    [schedulesEventTypes.REMINDER]: isCreationMode
      ? schedulesApi.createReminder
      : schedulesApi.updateReminder,
    [schedulesEventTypes.TASK]: isCreationMode
      ? schedulesApi.createTask
      : schedulesApi.updateTask,
  };

  const transform = {
    [schedulesEventTypes.MEETING]: ({
      duration,
      remind,
      externalConferenceProvider,
      externalConferenceProviderType,
      startTime,
      timezone,
      startDate,
      attendees,
      attachmentFiles,
      attendeePermissions,
      contactType,
      location,
      meetingChannel,
      customLink,
      ...rest
    }: any): BEMeetingDetails => ({
      ...rest,
      // attachmentFileIds,
      attendees: attendees?.map((attendee: any) => ({
        ...(attendee.userId
          ? {
              id: attendee.id,
              userId: attendee.userId,
            }
          : {
              userId: attendee.id,
            }),
        permissions: attendeePermissions.map(
          (permission: any) => permission.value
        ),
      })),
      duration: duration?.value,
      contactType: contactType?.value,
      remind: remind?.value,
      start: Time.getBackendDateTimeFromDateAndTime(
        startDate,
        startTime?.value,
        Number(timezone?.offset) || 0
      ),
      timezoneCode: timezone?.code,
      timezoneId: timezone?.value,
      timezoneLabel: timezone?.label,
      timezoneOffset: timezone?.offset,
      attachmentFileIds: attachmentFiles?.map((item: UploadedFile) =>
        Number(item?.id || 0)
      ),
      location: location?.[0]?.location,
      ...(meetingChannel === MeetingChannel.LOBOX_ACCOUNT
        ? {
            externalConferenceProviderType: ConferenceProviderName.LOBOX,
          }
        : meetingChannel === MeetingChannel.PERSONAL_ACCOUNT
          ? {
              externalConferenceProviderType:
                externalConferenceProvider?.value?.type,
              externalConferenceProviderId:
                externalConferenceProvider?.value?.id,
            }
          : {
              customLink,
            }),
    }),
    [schedulesEventTypes.REMINDER]: ({
      repeatType,
      startTime,
      startDate,
      allDay,
      ...rest
    }: any): BEReminderDetails => ({
      ...rest,
      repeatType: repeatType?.value,
      allDay: allDay || false,
      datetime: allDay
        ? dayjs(startDate).tz('UTC').format(BE_TIME_FORMAT)
        : Time.getBackendDateTimeFromDateAndTime(startDate, startTime?.value),
    }),
    [schedulesEventTypes.TASK]: ({
      startTime,
      endTime,
      // category,
      assignee,
      startDate,
      endDate,
      attachmentFiles,
      assigneePermission,
      status,
      ...rest
    }: any): BETaskDetails => ({
      ...rest,
      assignee: assignee
        ? {
            userId: assignee.id,
            modifyPermission: Boolean(assigneePermission?.[0]?.value),
          }
        : undefined,
      start: Time.getBackendDateTimeFromDateAndTime(
        startDate,
        startTime?.value
      ),
      end: Time.getBackendDateTimeFromDateAndTime(endDate, endTime?.value),
      attachmentFileIds: attachmentFiles?.map((item: UploadedFile) => item?.id),
      status: status.value,
    }),
  };
  const messages = {
    [schedulesEventTypes.MEETING]: isCreationMode
      ? t('meeting_add_success')
      : t('meeting_updated_success'),
    [schedulesEventTypes.REMINDER]: isCreationMode
      ? t('reminder_add_success')
      : t('reminder_updated_success'),
    [schedulesEventTypes.TASK]: isCreationMode
      ? t('task_add_success')
      : t('task_updated_success'),
  };

  const validationSchemas = {
    [schedulesEventTypes.MEETING]: formValidator.object().shape({
      startTime: timeValidator(),
      title: isEmptyValidator('meeting_title_required'),
      customLink: linkValidationNonNullable,
    }),
    [schedulesEventTypes.REMINDER]: formValidator.object().shape({
      startTime: timeValidator(),
    }),
    [schedulesEventTypes.TASK]: formValidator
      .object()
      .shape({
        startTime: timeValidator(),
        endTime: timeValidator({ hasAllDay: true }),
      })
      .test(
        'no-time-without-date',
        '',
        noTimeWithoutDate({
          dateFieldName: 'endDate',
          timeFieldName: 'endTime',
          dateErrorMessage: t('should_set_time'),
          timeErrorMessage: t('should_set_date_first'),
        })
      )
      .test(
        'endDate is after startDate',
        '',
        endDateAfterStartCB({ errorMessage: t('end_after_start') })
      ),
  };

  const refetchData = (res) => {
    if (isInCandidateManager) {
      return refetchMeetings();
    }

    if (isInScheduleMeetingTab) {
      const isUpcoming = Time.convertBackFormatToFront(res.start).isAfter(
        Time.getToday()
      );
      return isUpcoming ? refetchUpcoming() : refetchPast();
    }

    if (isInScheduleCalendarTab) {
      return refetchUpcomingAndPastEvents();
    }
  };

  const handlePostSuccessActions = () => {
    if (isCreationMode) {
      closeHandler();
    } else {
      queryResult.refetch();
      refetchAttachmentFiles();
      refetchProviders();
      backHandler();
    }
  };

  const onSuccessHandler = (res) => {
    refetchData(res);

    handleSuccess({ message: messages[schedulesEventType] });

    handlePostSuccessActions();

    fetchEvents();
  };

  return {
    initialValues: isCreationMode
      ? initialValues[schedulesEventType]
      : editInitialValues,
    apiFunc: apiFunc[schedulesEventType],
    transform: transform[schedulesEventType],
    validationSchema: validationSchemas[schedulesEventType],
    onSuccessHandler,
  };
};

export default useScheduleForm;
