import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import formatDate from '@shared/utils/toolkit/formatDate';
import classes from './index.module.scss';

const DateAndTimeShower = ({
  date,
  title,
  separator = 'divider',
  classNames,
}: {
  title?: string;
  date?: string;
  separator?: 'divider' | 'comma';
  classNames?: {
    date?: string;
    title?: string;
    divider?: string;
  };
}) => {
  const { t } = useTranslation();

  return (
    <Flex className={classes.root}>
      {title && (
        <Typography
          font="500"
          color="secondaryDisabledText"
          className={cnj(classes.date, classNames?.title)}
        >
          {title}
        </Typography>
      )}

      <Typography
        font="400"
        color="smoke_coal"
        variant="div"
        className={cnj(
          classes.time,
          !date ? 'opacity-30' : '',
          classNames?.date
        )}
      >
        {date ? (
          <>
            {formatDate(date, 'll')}
            {separator === 'divider' ? (
              <DividerVertical
                className={cnj(classes.divider, classNames?.divider)}
              />
            ) : (
              <Typography className={classNames?.divider}>,</Typography>
            )}
            {formatDate(date, 'LT')}
          </>
        ) : (
          t('not_set')
        )}
      </Typography>
    </Flex>
  );
};

export default DateAndTimeShower;
