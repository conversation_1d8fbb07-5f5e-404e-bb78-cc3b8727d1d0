import { useMemo } from 'react';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import BaseBusinessJobCard from './BaseBusinessJobCard';
import type {
  BaseBusinessJobCardClassNamesProps,
  CommonBaseBusinessJobCardProps,
} from './BaseBusinessJobCard';
import type { FC } from 'react';
import type { JobAPIProps } from 'shared/types/jobsProps';

export interface BusinessJobCardInListProps
  extends CommonBaseBusinessJobCardProps {
  job: JobAPIProps;
  classNames?: BaseBusinessJobCardClassNamesProps;
  showBadges?: boolean;
}

const BusinessJobCardInList: FC<BusinessJobCardInListProps> = (props) => {
  const { job, classNames, showBadges, ...rest } = props;

  const projects = useMemo(
    () =>
      typeof job.projects === 'string'
        ? job.projects
        : job.projects?.map((item) => item.title).join(', '),
    [job]
  );

  return (
    <BaseBusinessJobCard
      {...rest}
      id={job.id}
      image={job.pageCroppedImageUrl}
      title={job.title}
      username={job.pageTitle}
      category={job.categoryName ?? job.category}
      location={cleanRepeatedWords(job.location?.label || job.location)}
      creator={{
        name: job.ownerFullName ?? job.userFullName,
        username: job.ownerUsername ?? job.username,
      }}
      lastActivity={job?.lastActivity}
      projects={projects}
      createdAt={job.createdDate}
      status={job.status}
      priority={job.priority}
      classNames={classNames}
      collaboratorsCount={Number(
        job.collaborators?.length ?? job.collaboratorsCount
      )}
      applicantsCount={Number(job.applicantsCount)}
      candidatesCount={Number(job.candidatesCount)}
      lastUpdate={job.lastModifiedDate}
      inList
      showBadges={showBadges}
      projectIds={
        rest?.hasRedirection
          ? job.projects.map((project) => project.pageId)
          : undefined
      }
    />
  );
};

export default BusinessJobCardInList;
