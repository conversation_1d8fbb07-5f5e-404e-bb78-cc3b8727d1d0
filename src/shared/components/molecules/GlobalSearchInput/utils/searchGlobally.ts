import { searchCandidate } from '@shared/utils/api/candidates';
import { searchProjects } from '@shared/utils/api/project';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import jobsApi, { searchBusinessJob } from 'shared/utils/api/jobs';
import { getMostPopularHashtags } from 'shared/utils/api/postSearch';
import {
  suggestObjects,
  suggestHashtags,
  searchCompanies,
} from 'shared/utils/api/search';
import { getEvenItemsFromArrays } from './normalizers';
import { itemNormalizers, typeOrder } from './shared';
import type {
  SearchModuleType,
  TextArg,
} from 'shared/hooks/useGlobalSearchUtilities';

type Rtype = any[];

export type ItemType = 'PAGE' | 'PERSON' | 'HASHTAG' | 'JOB' | 'TEXT';

export async function searchGlobally(
  textArg: TextArg,
  currentModule: SearchModuleType
): Promise<Rtype> {
  const args: any = { size: 5, text: textArg?.params?.text };
  const isHashtagSign = textArg?.params?.text === '#';
  const isSearchAll = currentModule === 'all';

  if (isHashtagSign) {
    const res = await getMostPopularHashtags({
      size: 20,
      includefollowersCounter: true,
      includeMyFollowing: true,
    });

    return res?.content?.map((item) => ({
      type: 'HASHTAG',
      label: item?.id,
      title: item?.id,
    }));
  }

  const tryCatchWrapper = async (
    prom: (funcArgs: any) => Promise<any>,
    funcArgs: any,
    defaultValue: any
  ) => {
    let data: any = defaultValue;
    try {
      data = await prom(funcArgs);
    } catch (err) {
      console.warn(err);
    }

    return data;
  };
  const jobPromise: Promise<any[]> = new Promise((res) => {
    res(
      (currentModule === 'jobs' || isSearchAll) && !isBusinessApp
        ? tryCatchWrapper(jobsApi.searchJobsSuggest, { params: args }, [])
        : []
    );
  });

  const projectPromise: Promise<any[]> = new Promise((res) => {
    res(isBusinessApp ? tryCatchWrapper(searchProjects, args, []) : []);
  });
  const candidatePromise: Promise<any[]> = new Promise((res) => {
    res(isBusinessApp ? tryCatchWrapper(searchCandidate, args, []) : []);
  });
  const businessJobPromise: Promise<any[]> = new Promise((res) => {
    res(
      isBusinessApp
        ? tryCatchWrapper(searchBusinessJob, { params: args }, [])
        : []
    );
  });
  const companiesPromise: Promise<any[]> = new Promise((res) => {
    res(isBusinessApp ? tryCatchWrapper(searchCompanies, args, []) : []);
  });

  const personOrPageOrTextPromise: Promise<Required<{ content: any[] }>> =
    new Promise((res) => {
      res(
        (currentModule === 'pages' ||
          currentModule === 'people' ||
          isSearchAll) &&
          !isBusinessApp
          ? tryCatchWrapper(suggestObjects, args, { content: [] })
          : { content: [] }
      );
    });
  const hashtagPromise: Promise<Required<{ content: any[] }>> = new Promise(
    (res) => {
      res(
        (currentModule === 'hashtags' || isSearchAll) && !isBusinessApp
          ? tryCatchWrapper(suggestHashtags, args, { content: [] })
          : { content: [] }
      );
    }
  );

  const res = await Promise.all([
    jobPromise,
    personOrPageOrTextPromise,
    hashtagPromise,
    projectPromise,
    candidatePromise,
    businessJobPromise,
    companiesPromise,
  ]);
  const normalizedRes = normalizer(res, [
    itemNormalizers.job,
    itemNormalizers.personOrPageOrText,
    itemNormalizers.hashtag,
    itemNormalizers.project,
    itemNormalizers.candidate,
    itemNormalizers.businessJob,
    itemNormalizers.companies,
  ]);
  const stringJobs = normalizedRes[0]?.filter((item) => !item?.isSpecific);
  const jobs = normalizedRes[0]?.filter((item) => item?.isSpecific);
  const people = normalizedRes[1]?.filter(
    (item: any) => item?.type === 'PERSON'
  );
  const pages = normalizedRes[1]?.filter((item: any) => item?.type === 'PAGE');
  const hashtags = normalizedRes[2];
  const projects = normalizedRes[3];
  const candidates = normalizedRes[4];
  const recruiterJobs = normalizedRes[5];
  const companies = normalizedRes[6];

  if (isBusinessApp) {
    const currentModuleResult =
      { recruiterJobs, candidates, projects, companies }[currentModule] || [];

    if (currentModuleResult?.length) {
      return currentModuleResult;
    }

    return getEvenItemsFromArrays(
      [recruiterJobs, candidates, projects, companies],
      20,
      typeOrder
    );
  }
  const result = getEvenItemsFromArrays(
    [stringJobs, jobs, people, pages, hashtags],
    20,
    typeOrder
  );

  // if (currentGeneralModule !== 'all') {
  //   result?.sort((x, y) => {
  //     x.type === currentModuleMapper[currentGeneralModule]
  //       ? 1
  //       : y.type === currentModuleMapper[currentGeneralModule]
  //         ? -1
  //         : 0;
  //   });
  // }
  return result;
}

function normalizer(res: any[], normalizers: Function[]) {
  return res.map((group, index) => normalizers[index](group));
}
