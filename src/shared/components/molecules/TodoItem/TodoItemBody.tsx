import Divider from '@shared/uikit/Divider';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import { RichTextView } from '@shared/uikit/RichText';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import DateAndTimeShower from '../DateAndTimeShower';
import UserInfo from '../UserInfo';
import classes from './index.module.scss';
import type { ICandidateTodo } from '@shared/types/candidates';
import type { FC } from 'react';
import YouTodoItemBody from '@shared/components/molecules/TodoItem/YouTodoItemBody';
import isEmpty from '@shared/utils/toolkit/isEmpty';

export interface TodoItemBodyProps {
  item: ICandidateTodo;
  classNames?: {
    title?: string;
    description?: string;
  };
  displayCreator?: true;
  action?: ReactNode;
  variant?: 'card' | 'preview' | 'you';
}

const TodoItemBody: FC<TodoItemBodyProps> = (props) => {
  const { item, classNames, displayCreator, variant = 'card' } = props;
  console.log({ item });
  const { t } = useTranslation();
  const showAssignee = variant === 'card' && !isEmpty(item.assigneeUser?.id);
  const showCreator =
    displayCreator && variant === 'card' && !isEmpty(item.creator?.id);

  return variant !== 'you' ? (
    <Flex className={classes.body}>
      {variant === 'card' ? (
        <Flex className={'!w-full !justify-between !flex-row'}>
          <Typography
            className={cnj(
              classes.title,
              item.status?.value === 'DONE' ? classes.done : '',
              classNames?.title
            )}
            size={16}
            color="smoke_coal"
            font="700"
          >
            {item.title}
          </Typography>

          {variant === 'card' && (
            <RichTextView
              html={item?.description || ''}
              className={classNames?.description}
              typographyProps={{
                color: 'secondaryDisabledText',
                size: 14,
                height: 18,
              }}
              showMore
            />
          )}
        </Flex>
      ) : null}
      {showCreator || showAssignee ? <Divider /> : null}
      {showAssignee ? (
        <UserInfo
          id={item.assigneeUser.id}
          username={item.assigneeUser.username}
          title={`${item.assigneeUser.name || ''} ${item.assigneeUser.surname || ''}`}
          description={item.assigneeUser.occupationName || '=='}
          image={item.assigneeUser.croppedImageUrl}
          layoutTitle={t('assignee1')}
        />
      ) : null}
      {showCreator ? (
        <UserInfo
          id={item.creator.id}
          username={item.creator.username}
          title={`${item?.creator?.name || ''} ${item?.creator?.surname || ''}`}
          description={item?.creator?.occupationName as string}
          image={item?.creator?.croppedImageUrl}
          layoutTitle={t('creator1')}
        />
      ) : null}
      <Divider />
      <Flex flexDir="row" className={classes.dates}>
        <DateAndTimeShower
          title={t('start_date_time')}
          date={item.start}
          separator={'divider'}
        />
        <DividerVertical className={classes.divider} />
        <DateAndTimeShower
          title={t('end_date_time')}
          date={item.end}
          separator={'divider'}
        />
      </Flex>
    </Flex>
  ) : (
    <YouTodoItemBody
      item={item}
      classNames={classNames}
      action={props.action}
    />
  );
};

export default TodoItemBody;
