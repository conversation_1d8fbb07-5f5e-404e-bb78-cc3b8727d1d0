import cnj from '@shared/uikit/utils/cnj';
import CardWrapper from '../CardItem/CardWrapper';
import classes from './index.module.scss';
import TodoItemBody from './TodoItemBody';
import type { TodoItemBodyProps } from './TodoItemBody';
import type { CardWrapperProps } from '../CardItem/CardWrapper';
import type { FC, ReactNode } from 'react';

interface TodoItemProps extends TodoItemBodyProps {
  cardWrapperProps?: CardWrapperProps;
  action?: ReactNode;
}

const TodoItem: FC<TodoItemProps> = (props) => {
  const { cardWrapperProps, item, action } = props;

  return (
    <CardWrapper
      {...cardWrapperProps}
      classNames={{
        ...cardWrapperProps?.classNames,
        container: cnj(
          classes.container,
          cardWrapperProps?.classNames?.container
        ),
        root: cnj(classes.root, cardWrapperProps?.classNames?.root),
      }}
    >
      {props.variant !== 'portal' && action}
      <TodoItemBody
        {...props}
        action={props.variant === 'portal' && action}
        classNames={
          props?.variant === 'portal' ? { description: '!min-h-[72px]' } : {}
        }
      />
    </CardWrapper>
  );
};

export default TodoItem;
