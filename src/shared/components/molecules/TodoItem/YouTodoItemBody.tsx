import Divider from '@shared/uikit/Divider';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Avatars from '@shared/components/molecules/Avatars/Avatars';
import Flex from '@shared/uikit/Flex';
import { RichTextView } from '@shared/uikit/RichText';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import DateAndTimeShower from '../DateAndTimeShower';
import classes from './index.module.scss';
import type { ICandidateTodo } from '@shared/types/candidates';
import type { FC, ReactNode } from 'react';

export interface TodoItemBodyProps {
  item: ICandidateTodo;
  classNames?: {
    title?: string;
    description?: string;
  };
  action?: ReactNode;
}

const dateAndTimeShowerClassNames = {
  date: '!text-secondaryDisabledText',
  divider: '!text-secondaryDisabledText mr-2',
};

const YouTodoItemBody: FC<TodoItemBodyProps> = (props) => {
  const { item, classNames } = props;

  return (
    <Flex className={classes.body}>
      <Flex className={'!w-full !justify-between !flex-row'}>
        <Typography
          className={cnj(
            classes.title,
            item.status?.value === 'DONE' ? classes.done : '',
            classNames?.title
          )}
          size={18}
          color="smoke_coal"
          font="700"
        >
          {item.title}
        </Typography>
        {props?.action && props.action}
      </Flex>

      <RichTextView
        html={item?.description || ''}
        className={classNames?.description}
        typographyProps={{
          color: 'disabledGray',
          size: 14,
          height: 72,
          fontWeight: 500,
        }}
        showMore
      />

      <Divider />
      <Flex flexDir="row" className={cnj(classes.dates, 'relative')}>
        <DateAndTimeShower
          date={item.start}
          classNames={dateAndTimeShowerClassNames}
          separator={'comma'}
        />
        <DividerVertical className={classes.divider} />
        <DateAndTimeShower
          date={item.end}
          classNames={dateAndTimeShowerClassNames}
          separator={'comma'}
        />

        <Avatars
          avatars={[item.assigneeUser, item.creator]}
          onClick={() => {}}
          className="!ml-auto"
          avatarProps={{
            size: 'xs',
          }}
        />
      </Flex>
    </Flex>
  );
};

export default YouTodoItemBody;
