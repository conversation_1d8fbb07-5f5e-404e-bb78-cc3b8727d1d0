import React from 'react';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Divider from '@shared/uikit/Divider';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import cnj from '@shared/uikit/utils/cnj';
import classes from './index.module.scss';
import AvatarsSkeleton from '@shared/components/molecules/AvatarsCard/AvatarsCard.skeleton';

export default function YouTodoItemSkeleton() {
  return (
    <CardWrapper
      classNames={{
        root: cnj(classes.root, 'bg-background'),
        container: cnj(classes.container),
      }}
    >
      <Flex className={classes.body}>
        {/* Header with title and actions */}
        <Flex className="!w-full !justify-between !flex-row !items-center">
          <Skeleton className="!w-[120px] !h-[22px] rounded" />
          <Flex flexDir="row" className="gap-2">
            <Skeleton className="!w-[80px] !h-[22px] rounded" />
            <Skeleton className="!w-[24px] !h-[22px] rounded" />
          </Flex>
        </Flex>

        {/* Description content */}
        <Flex className="gap-2">
          <Skeleton className="!w-full !h-[18px] rounded" />
          <Skeleton className="!w-full !h-[18px] rounded" />
          <Skeleton className="!w-[60%] !h-[18px] rounded" />
          <Skeleton className="!w-[80px] !h-[16px] rounded" />
        </Flex>

        <Divider />

        {/* Footer with dates and avatars */}
        <Flex
          flexDir="row"
          alignItems="center"
          className={cnj(classes.dates, 'relative')}
        >
          <Flex className="gap-1">
            <Skeleton className="!w-[100px] !h-[32px] rounded" />
          </Flex>
          <DividerVertical className={classes.divider} />
          <Flex className="gap-1">
            <Skeleton className="!w-[100px] !h-[32px] rounded" />
          </Flex>

          {/* Overlapping avatars */}
          <Flex className="!ml-auto gap-[-8px]">
            <AvatarsSkeleton />
          </Flex>
        </Flex>
      </Flex>
    </CardWrapper>
  );
}
