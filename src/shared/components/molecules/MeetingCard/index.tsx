import { type FC } from 'react';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import DateAndTimeShower from '../DateAndTimeShower';
import ScheduleCardWrapper from '../ScheduleCard/ScheduleCardWrapper';
import classes from './meeting.module.scss';
import type { ScheduleCardWrapperProps } from '../ScheduleCard/ScheduleCardWrapper';
import type { MeetingProps } from '@shared/types/meeting';
import type { ButtonProps } from '@shared/uikit/Button';

interface ScheduleCardProps {
  cardProps?: ScheduleCardWrapperProps;
  actionProps?: ButtonProps;
  item: MeetingProps;
  isBrief?: boolean;
}

export const MeetingCard: FC<ScheduleCardProps> = (props) => {
  const { actionProps, cardProps, item } = props;
  const { t } = useTranslation();

  return (
    <ScheduleCardWrapper
      classNames={{ ...cardProps?.classNames, container: classes.container }}
      indicatorProps={{
        color: 'brand',
      }}
    >
      <Flex className={classes.titleBox}>
        <Typography className={classes.title} color="smoke_coal" font="700">
          {item?.meeting?.title}
        </Typography>
        <Typography className={classes.description} color="colorIconForth2">
          {t(item?.meeting?.type?.toLowerCase())}
        </Typography>
      </Flex>
      <DateAndTimeShower
        title={t('start_date_time')}
        date={item?.meeting?.start}
      />

      {!!actionProps && (
        <Button
          {...actionProps}
          schema={actionProps?.schema ?? 'semi-transparent'}
        />
      )}
    </ScheduleCardWrapper>
  );
};
