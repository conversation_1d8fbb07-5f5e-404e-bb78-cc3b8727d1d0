import React from 'react';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import ScheduleCardWrapper from '@shared/components/molecules/ScheduleCard/ScheduleCardWrapper';
import AvatarsSkeleton from '@shared/components/molecules/AvatarsCard/AvatarsCard.skeleton';
import classes from './meeting.module.scss';
import cnj from '@shared/uikit/utils/cnj';

export default function MeetingCardSkeleton() {
  return (
    <ScheduleCardWrapper
      classNames={{
        container: classes.container,
      }}
    >
      {/* Title and type section */}
      <Flex className={classes.titleBox}>
        <Skeleton className={classes.titleSkeleton} />
        <Skeleton className={classes.descSkeleton} />
      </Flex>

      {/* Date and time section */}
      <Flex className="gap-2">
        <Skeleton className="!w-[120px] !h-[16px] rounded" />
        <Flex className="gap-1">
          <Skeleton className="!w-[100px] !h-[16px] rounded" />
        </Flex>
      </Flex>

      {/* Attendees section */}
      <Flex className="gap-2">
        <Skeleton className="!w-[80px] !h-[16px] rounded" />
        <Flex flexDir="row" alignItems="center" className="gap-1 items-center">
          <AvatarsSkeleton />
          <Skeleton className="!w-[60px] !h-[16px] rounded ml-2" />
        </Flex>
      </Flex>

      {/* Action button */}
      <Skeleton className={cnj(classes.actionSkeleton, 'rounded')} />
    </ScheduleCardWrapper>
  );
}
