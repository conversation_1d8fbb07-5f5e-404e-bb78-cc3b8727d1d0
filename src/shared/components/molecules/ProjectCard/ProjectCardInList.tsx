import BaseProjectCardInList from './BaseProjectCardInList';
import type { CardWrapperProps } from '../CardItem/CardWrapper';
import type { FC } from 'react';
import type { ProjectAPIProps } from 'shared/types/projectsProps';

export interface ProjectCardInListProps extends CardWrapperProps {
  item: ProjectAPIProps;
}

const ProjectCardInList: FC<ProjectCardInListProps> = (props) => {
  const { item, ...rest } = props;

  return (
    <BaseProjectCardInList
      id={item.id}
      title={item.title}
      user={item.owner}
      lastActivity={item?.lastActivity}
      cardProps={{ ...rest }}
      applicantsCount={Number(item.applicantsCount)}
      collaboratorsCount={Number(item.collaborators.length || 0)}
      jobsCount={Number(item?.jobs?.length || 0)}
      time={item.lastModifiedDate}
      inList
      status={item.status}
    />
  );
};

export default ProjectCardInList;
