import { PipelineInfo } from '@shared/types/pipelineProps';
import Button from '@shared/uikit/Button';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';

const ReviewButton = ({
  pipelines,
  stageTitle,
  onChangePipeline,
}: {
  pipelines?: PipelineInfo[];
  stageTitle: string;
  onChangePipeline: (pipeline: string) => void;
}) => {
  const { t } = useTranslation();

  return (
    <PopperMenu
      placement="bottom"
      popperWidth={(width) => width}
      buttonComponent={() => (
        <Button
          schema="semi-transparent"
          fullWidth
          label={t(stageTitle)}
          labelProps={{ fontWeight: 700, size: 15 }}
          rightIcon="chevron-down"
          rightSize={12}
        />
      )}
    >
      {pipelines?.map((pipeline) => (
        <PopperItem
          key={pipeline.id}
          label={t(pipeline.title)}
          onClick={() => onChangePipeline(pipeline.id)}
        />
      ))}
    </PopperMenu>
  );
};

export default ReviewButton;
