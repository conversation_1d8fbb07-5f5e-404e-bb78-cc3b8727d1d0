import DateView from '@shared/uikit/DateView';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import { RichTextView } from '@shared/uikit/RichText';
import Tooltip from '@shared/uikit/Tooltip';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { dayjs } from '@shared/utils/Time';
import { PrivateAttachmentsList } from '../Attachments';
import UserInfo from '../UserInfo';
import classes from './index.module.scss';
import type { ICandidateNote } from '@shared/types/candidates';
import type { FC } from 'react';

export interface NoteItemBodyProps {
  item: ICandidateNote;
  action?: React.ReactNode;
  classNames?: {
    body?: string;
  };
  dateFormat?: string;
}

const NoteItemBody: FC<NoteItemBodyProps> = (props) => {
  const { item, action, classNames, dateFormat } = props;
  const { t } = useTranslation();

  return (
    <Flex className={classes.body}>
      <UserInfo
        id={item.id}
        username={item.username}
        title={`${item.name} ${item.surname}`}
        description={item.occupationName as string}
        image={item.croppedImageUrl}
        classNames={{ root: 'w-full' }}
        action={action}
      />
      {item.body && (
        <RichTextView
          html={item.body}
          className={classNames?.body}
          typographyProps={{
            size: 14,
            height: 18,
            font: '400',
          }}
          showMore
        />
      )}
      {item.fileIds ? (
        <PrivateAttachmentsList horizontal ids={item.fileIds} />
      ) : null}
      <Flex flexDir="row" className={classes.footer}>
        <Tooltip
          placement="top-start"
          disabled={!item.visibility.tooltip}
          trigger={
            <Flex flexDir="row" className="gap-4 items-center">
              <Icon
                name={item.visibility.icon}
                type={item.visibility.type}
                color="colorIconForth2"
                size={14}
              />
              <Typography
                font="500"
                color="colorIconForth2"
                size={15}
                height={17}
                textAlign="center"
              >
                {t(item.visibility.label)}
              </Typography>
            </Flex>
          }
        >
          {t(item.visibility.tooltip ?? '')}
        </Tooltip>
        {dateFormat ? (
          <Typography
            className="ml-auto"
            size={12}
            font="400"
            height={15.6}
            color="secondaryDisabledText"
          >
            {dayjs(item.createDateTime).format(dateFormat)}
          </Typography>
        ) : (
          <DateView
            value={item.createDateTime}
            className="ml-auto"
            size={12}
            font="400"
            height={15.6}
            color="secondaryDisabledText"
          />
        )}
      </Flex>
    </Flex>
  );
};

export default NoteItemBody;
