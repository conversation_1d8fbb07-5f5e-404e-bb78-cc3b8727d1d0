import { useRouter } from 'next/navigation';
import { type FC } from 'react';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import { routeNames } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  mapActivityToNoteItem,
  mapActivityToTodoItem,
} from '@shared/utils/normalizers/activityNormalizer';
import NoteItem from 'shared/components/molecules/NoteItem';
import TodoItem from 'shared/components/molecules/TodoItem';
import UserInfo from 'shared/components/molecules/UserInfo';
import ScheduleCard from '../../ScheduleCard';
import ActivityCondition from './ActivityBottomBox/ActivityCondition';
import ActivityConditionChanged from './ActivityBottomBox/ActivityConditionChanged';
import ActivityReview from './ActivityBottomBox/ActivityReview';
import type { ActivityConditionType } from './ActivityBottomBox/ActivityCondition';
import type { ActivityProps } from '@shared/types/activityProps';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type { ICandidateNote, ICandidateTodo } from '@shared/types/candidates';

interface ActivityBottomBoxProps {
  item: ActivityProps;
}

const ActivityBottomBox: FC<ActivityBottomBoxProps> = ({ item }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const appDispatch = useGlobalDispatch();

  const handleViewCandidate = () => {
    appDispatch({
      type: 'SET_ENTITY_ACTIVE_ROOM',
      payload: { tab: 'about' },
    });
    router.push(
      routeNames.candidate.makeRoute(`${item.candidateUser.id}&activeTab=about`)
    );
  };

  const handleViewJob = () =>
    router.push(`/search/my-jobs?currentEntityId=${item.job?.id}`);

  const handleViewProject = () =>
    router.push(`/search/my-projects?currentEntityId=${item.project?.id}`);

  const handleViewCandidateTabs = (tab: CandidateManagerTabkeys) => {
    appDispatch({
      type: 'TOGGLE_CANDIDATE_MANAGER',
      payload: {
        isOpen: true,
        enableNavigate: false,
        id: item.candidateUser.id,
        tab: tab as CandidateManagerTabkeys,
      },
    });
  };

  switch (item.type) {
    case 'PIPELINE_CHANGED': {
      return (
        <ActivityConditionChanged
          lastCondition={item.lastPipeline}
          condition={item.pipeline}
          renderItem={(pipeLine, isSecond) => (
            <ActivityCondition
              title={pipeLine.type}
              variant={avtivityCondition[pipeLine.type]}
              classNames={
                isSecond
                  ? {
                      root: 'opacity-50',
                    }
                  : undefined
              }
            />
          )}
        />
      );
    }
    case 'REVIEW_ADDED': {
      return (
        <ActivityReview rate={item.reviewScore || 0} text={item.reviewText} />
      );
    }
    case 'REVIEW_MODIFIED': {
      const beforeReview = (
        <ActivityReview
          rate={item.lastReviewScore || 0}
          text={item.lastReviewText}
          classNames={{ root: '!opacity-60' }}
        />
      );
      const afterReview = (
        <ActivityReview rate={item.reviewScore || 0} text={item.reviewText} />
      );

      return (
        <Flex alignItems="center" className="gap-16">
          {beforeReview}
          <Icon name="long-arrow-right" size={24} />
          {afterReview}
        </Flex>
      );
    }
    case 'REVIEW_REMOVED': {
      return (
        <ActivityReview
          rate={item.reviewScore || 0}
          text={item.reviewText}
          classNames={{ root: '!opacity-60' }}
        />
      );
    }
    case 'JOB_STATUS_CHANGED': {
      return (
        <ActivityConditionChanged
          lastCondition={item.lastJobStatus}
          condition={item.jobStatus}
          renderItem={(status) => (
            <ActivityCondition
              title={status}
              variant={avtivityCondition[status]}
            />
          )}
        />
      );
    }
    case 'JOB_PRIORITY_CHANGED': {
      return (
        <ActivityConditionChanged
          lastCondition={item.lastJobPriority}
          condition={item.jobPriority}
          renderItem={(priority) => (
            <ActivityCondition
              title={priority}
              variant={avtivityCondition[priority]}
            />
          )}
        />
      );
    }
    case 'USER_APPLIED':
    case 'USER_WITHDRAWN': {
      return (
        <Button
          label={t('view_candidate')}
          schema="semi-transparent"
          onClick={handleViewCandidate}
        />
      );
    }
    case 'CANDIDATE_RELINKED': {
      return (
        <Button
          label={t('view_candidate')}
          onClick={handleViewCandidate}
          schema="semi-transparent3"
          className="!w-fit"
        />
      );
    }
    case 'JOB_ADDED_TO_PROJECT': {
      return (
        <Button
          label={t('view_job')}
          onClick={handleViewJob}
          schema="semi-transparent3"
          className="!w-fit"
        />
      );
    }
    case 'COLLABORATOR_ADDED_TO_PROJECT':
    case 'COLLABORATOR_REMOVED_FROM_PROJECT': {
      return (
        <UserInfo
          title={`${item.collaborator?.name} ${item.collaborator?.surname}`}
          description={t('collaborator')}
          image={item.collaborator?.croppedImageUrl}
          extraTitle={item.collaborator?.occupationName}
        />
      );
    }
    case 'CANDIDATE_SELECTED':
      return (
        <Button
          label={t('view_job')}
          onClick={handleViewJob}
          schema="semi-transparent3"
          className="!w-fit"
        />
      );
    case 'MEETING_SCHEDULED':
    case 'MEETING_CANCELED':
    case 'MEETING_REMOVED': {
      return (
        <ScheduleCard
          item={{
            meeting: {
              title: item.meetingTitle || '-',
              id: item.meetingId,
              type: 'MEETING',
              start: item.meetingStartDateTime,
            } as any,
          }}
          isBrief
          cardProps={{
            classNames: {
              root: '!bg-transparent border border-solid !border-techGray_20',
            },
          }}
        />
      );
    }
    case 'MEETING_MODIFIED': {
      return (
        <ActivityConditionChanged
          lastCondition={item}
          condition={item}
          variant="full"
          renderItem={(meeting, isSecond) => (
            <ScheduleCard
              item={{
                meeting: {
                  title: isSecond
                    ? meeting.lastMeetingTitle || '-'
                    : meeting.meetingTitle || '-',
                  id: meeting.meetingId,
                  type: 'MEETING',
                  start: isSecond
                    ? meeting.lastMeetingStartDateTime
                    : meeting.meetingStartDateTime,
                } as any,
              }}
              isBrief
              cardProps={{
                classNames: {
                  root: '!bg-transparent border border-solid !border-techGray_20',
                },
              }}
            />
          )}
        />
      );
    }

    case 'NOTE_ADDED':
    case 'NOTE_MODIFIED':
    case 'NOTE_REMOVED': {
      if (item.type === 'NOTE_MODIFIED') {
        const beforeNote = {
          ...mapActivityToNoteItem(item),
          text: item.lastNoteText,
        };
        const afterNote = {
          ...mapActivityToNoteItem(item),
          text: item.noteText,
        };

        return (
          <Flex alignItems="center" className="gap-16">
            <NoteItem
              item={beforeNote as ICandidateNote}
              cardWrapperProps={{ classNames: { root: '!opacity-60' } }}
            />
            <Icon name="long-arrow-right" size={24} />
            <NoteItem item={afterNote as ICandidateNote} />
          </Flex>
        );
      }

      return (
        <NoteItem
          item={mapActivityToNoteItem(item) as ICandidateNote}
          cardWrapperProps={{
            classNames: {
              root: item.type === 'NOTE_REMOVED' ? 'opacity-60' : '',
            },
          }}
        />
      );
    }
    case 'TODO_MODIFIED': {
      const beforeTodo = mapActivityToTodoItem(item, true);
      const afterTodo = mapActivityToTodoItem(item, false);

      return (
        <Flex alignItems="center" flexDir="row" className="gap-16">
          <TodoItem
            item={beforeTodo as ICandidateTodo}
            displayCreator
            cardWrapperProps={{ classNames: { root: '!opacity-60' } }}
          />
          <Icon name="long-arrow-right" size={24} />
          <TodoItem item={afterTodo as ICandidateTodo} displayCreator />
        </Flex>
      );
    }
    case 'TODO_ADDED':
    case 'TODO_REMOVED': {
      return (
        <TodoItem
          item={mapActivityToTodoItem(item) as ICandidateTodo}
          displayCreator
          cardWrapperProps={{
            classNames: {
              root: item.type === 'TODO_REMOVED' ? 'opacity-60' : '',
            },
          }}
        />
      );
    }

    case 'CANDIDATE_REJECTED':
    case 'CANDIDATE_SUBMITTED':
    case 'CANDIDATE_WITHDRAWN': {
      return (
        <Button
          label={t('view_candidate')}
          schema="semi-transparent"
          onClick={handleViewCandidate}
        />
      );
    }
    case 'JOB_UPDATED':
    case 'JOB_SUBMITTED_AS_CLIENT':
    case 'JOB_WITHDRAWN_AS_CLIENT': {
      return (
        <Button
          label={t('view_job')}
          schema="semi-transparent3"
          onClick={handleViewJob}
          className="!w-fit"
        />
      );
    }
    case 'SCOREBOARD_UPDATED': {
      return (
        <Button
          label={t('view_scoreboard')}
          schema="semi-transparent"
          onClick={() => handleViewCandidateTabs('notes')}
        />
      );
    }
    case 'DOCUMENT_UPDATED': {
      return (
        <Button
          label={t('view_documents')}
          schema="semi-transparent"
          onClick={() => handleViewCandidateTabs('documents')}
        />
      );
    }
    case 'PROJECT_UPDATED': {
      return (
        <Button
          label={t('view_project')}
          schema="semi-transparent"
          onClick={handleViewProject}
        />
      );
    }
    default: {
      return null;
    }
  }
};

export default ActivityBottomBox;

const avtivityCondition: { [key in string]: ActivityConditionType } = {
  OPEN: 'blue',
  CLOSED: 'default',
  LOW: 'blue',
  MEDIUM: 'orange',
  HIGH: 'red',
  CRITICAL: 'error',
};
