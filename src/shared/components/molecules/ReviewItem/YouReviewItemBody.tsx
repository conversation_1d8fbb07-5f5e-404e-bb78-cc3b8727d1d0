import Icon from '@shared/uikit/Icon';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import Rating from '../Rating';
import UserInfo from '../UserInfo';
import type { ReviewProps } from '@shared/types/review';
import type { FC } from 'react';
import dateFromNow from '@shared/utils/toolkit/dateFromNow';
import Avatar from '@shared/uikit/Avatar';
import Button from '@shared/uikit/Button';
import useHistory from '@shared/utils/hooks/useHistory';
import { routeNames } from '@shared/utils/constants';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { searchCandidateItem } from '@shared/utils/api/candidates';

export interface ReviewItemBodyProps {
  item: ReviewProps;
  action?: React.ReactNode;
}

const YouReviewItem: FC<ReviewItemBodyProps> = ({ item, action }) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const router = useHistory();

  const redirectToReviews = () => {
    router.push(routeNames.candidate.makeRoute(item.user.id));
    appDispatch({
      type: 'TOGGLE_CANDIDATE_MANAGER',
      payload: {
        isOpen: true,
        entityId: item.user.id,
        tab: 'reviews',
        enablePagination: false,
        apiFunc: searchCandidateItem,
      },
    });
  };
  return (
    <Flex className="!gap-20">
      <Flex flexDir="row" className="justify-between">
        <Flex>
          <UserInfo
            title={
              <Flex flexDir="row" className="!gap-8 items-center">
                <Typography
                  height={19}
                  color="smoke_coal"
                  font="bold"
                  size={16}
                >
                  {`${item.user.name} ${item.user.surname}`}
                </Typography>
              </Flex>
            }
            description={item.job?.title}
            image={item.user.croppedImageUrl}
            classNames={{ root: 'w-full' }}
            action={action}
          />
        </Flex>
        <Typography size={12} color="muteMidGray">
          {dateFromNow(item.createdDate, false, t)}
        </Typography>
      </Flex>
      <Flex className="bg-gray_5 p-12 !gap-12 rounded-lg">
        <Flex flexDir="row" className="justify-between">
          <Rating value={item.score} size={18} spacing={3} readOnly />
          <Flex flexDir="row" alignItems="center" className="gap-4">
            <Icon
              name={item.visibility?.icon}
              type="far"
              size={13}
              color="brand"
            />
            <Typography size={15} font="500" color="brand">
              {t(item.visibility?.label)}
            </Typography>
          </Flex>
        </Flex>
        <Flex>
          <RichTextView
            html={item.text}
            typographyProps={{
              size: 16,
              color: 'thirdText',
              height: 21,
            }}
            showMore
          />
          <Flex flexDir="row" alignItems="center" className="gap-4 mt-12">
            <Avatar
              bordered
              size="xxs"
              imgSrc={item?.writer?.croppedImageUrl}
            />
            <Flex flexDir="row" alignItems="center" className="gap-4">
              <Typography size={12} color="muteMidGray" font="500">
                {t('written_by')}
              </Typography>
              <Typography size={16} font="500" color="thirdText">
                {`${item?.writer?.name} ${item?.writer?.surname}`}
              </Typography>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
      <Flex flexDir="row" className="justify-between mt-12">
        <Flex>
          <Typography
            color="muteMidGray_disabledGray"
            font="500"
            size={12}
            className="px-20"
          >
            {t('linked_with')}
          </Typography>
          <Typography font="500">{item?.job?.title}</Typography>
        </Flex>
        <Button
          schema="semi-transparent"
          label={t('view_reviews')}
          onClick={redirectToReviews}
        />
      </Flex>
    </Flex>
  );
};

export default YouReviewItem;
