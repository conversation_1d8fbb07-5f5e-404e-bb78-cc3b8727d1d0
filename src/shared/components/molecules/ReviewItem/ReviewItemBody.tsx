import Icon from '@shared/uikit/Icon';
import { RichTextView } from '@shared/uikit/RichText';
import Tooltip from '@shared/uikit/Tooltip';
import useTranslation from '@shared/utils/hooks/useTranslation';
import formatDate from '@shared/utils/toolkit/formatDate';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import Rating from '../Rating';
import UserInfo from '../UserInfo';
import type { ReviewProps } from '@shared/types/review';
import type { FC } from 'react';
import YouReviewItem from '@shared/components/molecules/ReviewItem/YouReviewItemBody';

export interface ReviewItemBodyProps {
  item: ReviewProps;
  action?: React.ReactNode;
  variant?: 'candidate' | 'default' | 'you';
}

const ReviewItem: FC<ReviewItemBodyProps> = ({
  item,
  action,
  variant = 'default',
}) => {
  const { t } = useTranslation();

  return variant !== 'you' ? (
    <Flex className="!gap-20">
      <UserInfo
        title={
          <Flex flexDir="row" className="!gap-8 items-center">
            <Typography height={19} color="smoke_coal" font="bold" size={16}>
              {`${item.user.name} ${item.user.surname}`}
            </Typography>
            {variant === 'default' ? (
              <Rating value={item.score} spacing={3} readOnly />
            ) : (
              <Tooltip
                trigger={
                  <Icon
                    name={item.visibility?.icon}
                    type="far"
                    size={13}
                    color="brand"
                  />
                }
              >
                {t(item.visibility?.label)}
              </Tooltip>
            )}
          </Flex>
        }
        description={formatDate(item.createdDate, 'll - LT')}
        image={item.user.croppedImageUrl}
        classNames={{ root: 'w-full' }}
        action={action}
      />
      <Flex className="!gap-8">
        {variant === 'candidate' ? (
          <Rating value={item.score} spacing={3} readOnly />
        ) : null}
        <RichTextView
          html={item.text}
          typographyProps={{
            size: 15,
            color: 'thirdText',
            height: 21,
          }}
          showMore
        />
      </Flex>
    </Flex>
  ) : (
    <YouReviewItem item={item} action={action} />
  );
};

export default ReviewItem;
