import React from 'react';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import UserInfoSkeleton from '@shared/components/molecules/UserInfo/UserInfoSkeleton';
import cnj from '@shared/uikit/utils/cnj';
import classes from './index.module.scss';

export default function YouReviewItemSkeleton() {
  return (
    <CardWrapper
      classNames={{
        container: cnj(classes.container),
        root: cnj(classes.root),
      }}
    >
      <Flex className="!gap-20">
        {/* Header section */}
        <Flex flexDir="row" className="justify-between">
          <Flex>
            <UserInfoSkeleton />
          </Flex>
          <Skeleton className="!w-[60px] !h-[16px] rounded" />
        </Flex>

        {/* Main content box */}
        <Flex className="bg-gray_5 p-12 !gap-12 rounded-lg">
          {/* Rating and visibility section */}
          <Flex flexDir="row" className="justify-between">
            <Flex flexDir="row" className="gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="!w-[18px] !h-[18px] rounded" />
              ))}
            </Flex>
            <Flex flexDir="row" alignItems="center" className="gap-4">
              <Skeleton className="!w-[13px] !h-[13px] rounded" />
              <Skeleton className="!w-[60px] !h-[16px] rounded" />
            </Flex>
          </Flex>

          {/* Review text content */}
          <Flex className="gap-2">
            <Skeleton className="!w-full !h-[21px] rounded" />
            <Skeleton className="!w-full !h-[21px] rounded" />
            <Skeleton className="!w-full !h-[21px] rounded" />
            <Skeleton className="!w-[80%] !h-[21px] rounded" />
          </Flex>

          {/* Author attribution */}
          <Flex flexDir="row" alignItems="center" className="gap-4 mt-12">
            <Skeleton className="rounded-full !w-[24px] !h-[24px]" />
            <Flex flexDir="row" alignItems="center" className="gap-4">
              <Skeleton className="!w-[80px] !h-[16px] rounded" />
              <Skeleton className="!w-[120px] !h-[16px] rounded" />
            </Flex>
          </Flex>
        </Flex>

        {/* Footer section */}
        <Flex flexDir="row" className="justify-between mt-12">
          <Flex>
            <Skeleton className="!w-[80px] !h-[16px] rounded mb-2" />
            <Skeleton className="!w-[150px] !h-[16px] rounded" />
          </Flex>
          <Skeleton className="!w-[80px] !h-[32px] rounded" />
        </Flex>
      </Flex>
    </CardWrapper>
  );
}
