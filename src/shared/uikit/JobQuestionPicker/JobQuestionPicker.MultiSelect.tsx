import React, { useState } from 'react';
import DropdownSelect from 'shared/uikit/AutoComplete/DropdownSelect';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AlertMessage from '../AlertMessage';
import IconButton from '../Button/IconButton';
import CheckBox from '../CheckBox';
import Flex from '../Flex/Flex.component';
import TextInput from '../TextInput';
import Typography from '../Typography';
import classes from './JobQuestionPicker.MultiSelect.module.scss';
import { QUESTION_TYPE } from './types';
import type { IJobQuestionChildProps } from './types';

const MAXIMUM_LIMIT = 10;

const JobQuestionPickerMultiSelect = ({
  questionTypeOptions,
  type,
  dropDownChange,
  questionInputChange,
  questionValue,
  isFocus: isQuestionFocus,
  onFocus,
  questionChoices,
  isMustHave,
  addAnotherClick,
  deleteAnswer,
  answerChangeInput,
  requiredChange,
  dropDownClassName,
  questionType,
}: IJobQuestionChildProps) => {
  const [isFocus, setIsFocus] = useState<boolean | number>(false);
  const { t } = useTranslation();

  return (
    <>
      <DropdownSelect
        label={t('question_type')}
        options={questionTypeOptions}
        value={type}
        onChange={dropDownChange}
        className={dropDownClassName}
      />
      <Flex className={classes.title}>
        <TextInput
          {...{
            label: t('question'),
            onChange: questionInputChange,
            value: questionValue,
            isFocus: isQuestionFocus,
            onFocus,
          }}
        />
      </Flex>

      <Flex>
        <Typography font="500" size={16} height={20} className={classes.answer}>
          {questionType === QUESTION_TYPE.multiSelect
            ? t('choices')
            : t('selections')}
        </Typography>
        {questionChoices?.map((answer: any, index: number) => (
          <Flex className={classes.option} key={index}>
            <TextInput
              label={`${t(`option`)} ${index + 1}`}
              onChange={(e: any) => answerChangeInput?.(e, index, answer.id)}
              value={answer.answer}
              rightIcon={
                index > 1 ? (
                  <IconButton
                    type="far"
                    name="trash"
                    size="md"
                    onClick={() => deleteAnswer?.(index)}
                  />
                ) : null
              }
              isFocus={isFocus === index}
              onFocus={(e: boolean) => setIsFocus(e ? index : false)}
            />
          </Flex>
        ))}
      </Flex>
      <Flex flexDir="row" className={classes.footer}>
        <IconButton
          type="fas"
          size="md15"
          name="plus"
          colorSchema="backgroundIconSecondary"
          onClick={addAnotherClick}
          className={classes.addAnotherBtn}
          disabled={questionChoices?.length === MAXIMUM_LIMIT}
        />
        <CheckBox
          label={t('must_have_skl')}
          onChange={requiredChange}
          value={isMustHave}
          classNames={{
            containerRoot: classes.mustHave,
            checkbox: classes.mustHaveCheckbox,
          }}
          labelProps={{ font: '400' }}
        />
      </Flex>
      {questionChoices?.length === MAXIMUM_LIMIT && (
        <AlertMessage
          title={t('reach_max_error')}
          type="error"
          closeAble={false}
          className={classes.alert}
        />
      )}
    </>
  );
};

export default JobQuestionPickerMultiSelect;
