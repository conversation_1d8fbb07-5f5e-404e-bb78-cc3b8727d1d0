import React from 'react';
import DropdownSelect from 'shared/uikit/AutoComplete/DropdownSelect';
import useTranslation from 'shared/utils/hooks/useTranslation';
import CheckBox from '../CheckBox';
import Flex from '../Flex';
import TextInput from '../TextInput';
import classes from './JobQuestionPicker.TextInput.module.scss';
import type { IJobQuestionChildProps } from './types';

const JobQuestionPickerTextInput = ({
  questionTypeOptions,
  type,
  dropDownChange,
  questionInputChange,
  questionValue,
  isFocus,
  onFocus,
  isMustHave,
  requiredChange,
  dropDownClassName,
}: Omit<IJobQuestionChildProps, 'questionType'>) => {
  const { t } = useTranslation();

  return (
    <>
      <DropdownSelect
        label={t('question_type')}
        options={questionTypeOptions}
        value={type}
        onChange={dropDownChange}
        className={dropDownClassName}
      />
      <Flex className={classes.input}>
        <TextInput
          {...{
            label: t('question'),
            onChange: questionInputChange,
            value: questionValue,
            isFocus,
            onFocus,
          }}
        />
      </Flex>
      <CheckBox
        label={t('must_have_skl')}
        onChange={requiredChange}
        value={isMustHave}
        classNames={{
          containerRoot: classes.mustHave,
          checkbox: classes.mustHaveCheckbox,
        }}
        labelProps={{ font: '400' }}
      />
    </>
  );
};

export default JobQuestionPickerTextInput;
