import { useFormikContext } from 'formik';
import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import IconButton from '../Button/IconButton';
import CheckBox from '../CheckBox';
import Flex from '../Flex/Flex.component';
import TextInput from '../TextInput';
import Tooltip from '../Tooltip';
import Typography from '../Typography';
import cnj from '../utils/cnj';
import classes from './AddExternalJobLink.component.module.scss';

export interface AddExternalJobLinkProps {
  className?: string;
  textInputClassName?: string;
  placeholder?: string;
}

const AddExternalJobLink: React.FC<AddExternalJobLinkProps> = ({
  className,
  ...rest
}) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const { t } = useTranslation();

  const onChangeExtraLinkHandler = (value: boolean) => {
    setFieldValue('isAddExternalJobLink', value);
  };

  return (
    <Flex className={cnj(classes.externalLinkWrapper, className)}>
      <CheckBox
        value={values?.isAddExternalJobLink}
        onChange={onChangeExtraLinkHandler}
        label={
          <Flex className={cnj(classes.textContainer)}>
            <Typography color="thirdText" size={15}>
              {t('add_ext_link')}
            </Typography>
            <Tooltip
              trigger={
                <IconButton
                  colorSchema="primary"
                  type="far"
                  name="info-circle"
                  size="sm"
                />
              }
            >
              <Typography
                className={classes.toolTipContent}
                size={14}
                font="400"
                height={18}
                color="tooltipText"
              >
                {t('external_job_ref_helper_txt')}
              </Typography>
            </Tooltip>
          </Flex>
        }
        classNames={{
          containerRoot: classes.checkbox,
        }}
      />
      {values?.isAddExternalJobLink && (
        <Flex className={classes.inputWrap}>
          <TextInput
            {...rest}
            className={classes.textInput}
            name="websiteUrl"
            maxLength={100}
            required
            label={t(rest?.placeholder ?? 'web_address')}
          />
        </Flex>
      )}
    </Flex>
  );
};

export default AddExternalJobLink;
