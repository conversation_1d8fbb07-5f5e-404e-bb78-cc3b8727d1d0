import isString from 'lodash/isString';
import React from 'react';
import addClassToNode from 'shared/utils/addClassToNode';
import IconButton from '../Button/IconButton';
import Flex from '../Flex';
import Typography from '../Typography';
import cnj from '../utils/cnj';
import classes from './index.module.scss';
import type { IconButtonColorSchema } from '../Button/IconButton';

type StyleProps = {
  root?: string;
  checkbox?: string;
  textContainer?: string;
  label?: string;
  helperText?: string;
  containerRoot?: string;
};

interface CheckBoxProps {
  value?: any;
  label?: string | ReactNode;
  rightComponent?: ReactNode;
  helperText?: any;
  error?: any;
  onChange?: any;
  font?: any;
  classNames?: Partial<StyleProps>;
  labelProps?: any;
  defaultSchema?: IconButtonColorSchema;
  selectedSchema?: IconButtonColorSchema;
  disabled?: boolean;
  intermediate?: boolean;
}

const CheckBox = ({
  value,
  label,
  helperText,
  error,
  onChange,
  font,
  classNames,
  labelProps = {},
  selectedSchema = 'tertiary-transparent',
  defaultSchema = 'transparent',
  disabled,
  intermediate,
  rightComponent,
}: CheckBoxProps) => {
  const onChangeHandler = (e) => {
    if (disabled) return;
    onChange?.(!value, e);
  };

  return (
    <Flex flexDir="row" className={cnj('items-center gap-8', classNames?.root)}>
      <Flex
        className={cnj(
          classes.checkBoxRoot,
          classNames?.containerRoot,
          'w-full flex-row items-center'
        )}
      >
        <IconButton
          className={cnj(
            classes.icon,
            classNames?.checkbox,
            value && classes.selectedIcon,
            error && classes.errorBoundary,
            disabled && classes.disabled
          )}
          name={
            intermediate
              ? 'intermediate-square'
              : value
                ? 'check-square'
                : 'square'
          }
          type="far"
          colorSchema={value ? selectedSchema : defaultSchema}
          onClick={onChangeHandler}
          iconProps={{
            size: 22,
          }}
          size="sm20"
        />

        {label && isString(label) && (
          <Flex
            onClick={onChangeHandler}
            className={cnj(
              classes.textContainer,
              !disabled && classes.pointer,
              helperText && classes.marginTop,
              classNames?.textContainer
            )}
          >
            <Typography
              color={disabled ? 'primaryDisabledText' : 'thirdText'}
              size={15}
              ml={-2}
              font={font}
              className={classes?.label}
              {...labelProps}
            >
              {label}
            </Typography>
            {helperText && (
              <Typography
                color="thirdText"
                size={15}
                className={classes?.helperText}
              >
                {helperText}
              </Typography>
            )}
          </Flex>
        )}
        {label && !isString(label) && addClassToNode(label, classes.pointer)}
      </Flex>
      {rightComponent}
    </Flex>
  );
};

export default CheckBox;
