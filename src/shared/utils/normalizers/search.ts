import type { PaginateResponse } from 'shared/types/response';
import type {
  ISearchHashtag,
  ISearchHashtagApi,
  ISearchPage,
  ISearchPageApi,
  ISearchPeople,
  ISearchPeopleApi,
  ISearchPostApi,
  ISuggestHashtag,
  ISuggestHashtagApi,
  ISuggestObject,
  ISuggestObjectApi,
  ISearchPost,
  ISuggestPageMember,
  ISearchTeamMembers,
} from 'shared/types/search';

const itemNormalizer = (curr: any) => {
  if (!curr) return null;

  return {
    ...curr,
    ...curr.networkModel,
    locationTitle: curr.locations[0]?.title,
    type: curr.userType,
    fullName:
      curr.userType === 'PAGE'
        ? (curr.title as string)
        : `${curr.name} ${curr.surname}`,
    subTitle:
      curr.userType === 'PAGE' ? curr.industryName : curr.occupationName,
  };
};

const searchPersonNormalizer = (
  data: PaginateResponse<ISearchPeopleApi>
): PaginateResponse<ISearchPeople> => ({
  ...data,
  content: data.content.reduce(
    (prev: Array<ISearchPeople>, curr: ISearchPeopleApi) => {
      if (curr.hideIt) {
        return prev;
      }

      return [...prev, itemNormalizer(curr)];
    },
    []
  ),
  currentEntity: itemNormalizer(data?.currentEntity),
});

const searchObjectNormalizer = (
  data: PaginateResponse<ISuggestObjectApi>
): PaginateResponse<ISuggestObject> => ({
  ...data,
  content: data.content.reduce(
    (prev: Array<ISuggestObject>, curr: ISuggestObjectApi) => {
      if (curr.hideIt) {
        return prev;
      }

      return [
        ...prev,
        {
          ...curr,
          type: curr.userType,
          avatar: curr.croppedImageUrl,
          fullName:
            curr.userType === 'PAGE'
              ? (curr.title as string)
              : `${curr.name} ${curr.surname}`,
          subTitle:
            curr.userType === 'PAGE'
              ? curr.industryName
              : (curr.occupationName as any),
          usernameAtSign: `@${curr.username}`,
          isPage: curr.userType === 'PAGE',
        },
      ];
    },
    []
  ),
});

const suggestHashtagsNormalizer = (
  data: PaginateResponse<ISuggestHashtagApi>
): PaginateResponse<ISuggestHashtag> => ({
  ...data,
  content: data.content.map((item) => ({
    title: item.id,
    usageCount: item.usageCount,
    type: 'HASHTAG',
  })),
});

const singlePageNormalizer = (curr: any) => {
  if (!curr) return null;

  return {
    ...curr,
    ...curr.networkModel,
    locationTitle: curr.locations[0]?.title,
    type: 'PAGE',
  };
};

const singleCompanyNormalizer = (curr: any) => {
  if (!curr) return null;

  return {
    ...curr,
    ...curr.networkModel,
    // vendorId: activeTab === 'vendors' ? curr.id : undefined,
    // clientId: activeTab === 'clients' ? curr.id : undefined,
    id: curr.pageId || curr.id,
    locationTitle: curr.locations?.[0]?.title,
    type: 'PAGE',
    isFake: curr.isFake,
  };
};

const searchPageNormalizer = (
  data: PaginateResponse<ISearchPageApi>
): PaginateResponse<ISearchPage> => ({
  ...data,
  content: data.content.reduce((prev: Array<ISearchPage>, curr: any) => {
    if (curr.hideIt) {
      return prev;
    }

    return [...prev, singlePageNormalizer(curr)];
  }, []),
  currentEntity: singlePageNormalizer(data?.currentEntity),
});

const searchCompanyNormalizer = (
  data: PaginateResponse<ISearchPageApi>,
  activeTab: string
): PaginateResponse<ISearchPage> => ({
  ...data,
  content: data.content.reduce((prev: Array<ISearchPage>, curr: any) => {
    if (curr.hideIt) {
      return prev;
    }

    return [...prev, singleCompanyNormalizer(curr, activeTab)];
  }, []),
  currentEntity: singleCompanyNormalizer(data?.currentEntity, activeTab),
});

const searchHashtagsNormalizer = (
  data: PaginateResponse<ISearchHashtagApi>
): PaginateResponse<ISearchHashtag> => ({
  ...data,
  content: data.content.map((item) => ({
    ...item,
    title: item.id,
    type: 'HASHTAG',
  })),
});

const searchPostsNormalizer = (
  data: PaginateResponse<ISearchPostApi>
): PaginateResponse<ISearchPost> => ({
  ...data,
  content: data.content.map((item) => ({
    ...item,
    type: 'POST',
  })),
});

const objectDetailByIdNormalizer = (
  data: Array<ISuggestObjectApi>
): { [key: string]: ISuggestObject } =>
  data?.reduce(
    (prev, curr) => ({
      ...prev,
      [curr.id]: {
        ...curr,
        type: curr.userType,
        fullName:
          curr.userType === 'PAGE'
            ? (curr.title as string)
            : `${curr.name} ${curr.surname}`,
        subTitle:
          curr.userType === 'PAGE' ? curr.industryName : curr.occupationName,
      },
    }),
    {}
  );

const suggestPageMembersNormalizer = (data: ISuggestPageMember[]) =>
  data.map((item) => ({
    ...item,
    type: item.userType,
    fullName:
      item.userType === 'PAGE'
        ? (item.title as string)
        : `${item.name} ${item.surname}`,
  }));

const searchNormalizer = {
  person: searchPersonNormalizer,
  object: searchObjectNormalizer,
  suggestHashtags: suggestHashtagsNormalizer,
  page: searchPageNormalizer,
  company: searchCompanyNormalizer,
  hashtag: searchHashtagsNormalizer,
  post: searchPostsNormalizer,
  objectDetailById: objectDetailByIdNormalizer,
  suggestPageMembers: suggestPageMembersNormalizer,
};

export default searchNormalizer;
