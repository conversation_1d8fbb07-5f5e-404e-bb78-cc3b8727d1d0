import {
  db,
  jobsDb,
  jobSelectedKeys,
  jobStatuses,
} from 'shared/utils/constants/enums';
import collectionToObjectByKey from '../toolkit/collectionToObjectByKey';
import beforeCachePageDetail from './beforeCachePageDetail';
import beforeCacheUserInfo from './beforeCacheUserInfo';
import languageNormalizer from './languageNormalizer';
import skillNormalizer from './skillNormalizer';
import type {
  CandidateJobCardProps,
  CandidateProps,
} from '@shared/types/jobsProps';
import type {
  IJob,
  CompanyInfoType,
  IJobApi,
  CandidacyLinkedJobsParamsType,
  CandidacyLinkedJobsResponseType,
} from 'shared/types/job';
import type { PaginateResponse } from 'shared/types/response';
import type { UserType } from 'shared/types/user';

const employments = collectionToObjectByKey(db.EMPLOYMENT_TYPES);
const experienceLevels = collectionToObjectByKey(jobsDb.experienceLevels);
const salaryPeriods = collectionToObjectByKey(jobsDb.SALARY_PERIOD);
const workPlaceTypes = collectionToObjectByKey(jobsDb.WORK_SPACE_MODEL);
const jobStats = collectionToObjectByKey(jobStatuses);
const questionTypes = collectionToObjectByKey(jobsDb.QUESTION_TYPES);

export const jobItemNormalizer = ({
  employmentType,
  categoryId,
  categoryName,
  experienceLevel,
  skills,
  languages,
  salaryPeriod,
  salaryCurrencyId,
  salaryCurrencySymbol,
  salaryCurrencyName,
  salaryCurrencyCode,
  location,
  title,
  titleId,
  languageId,
  workPlaceType,
  status,
  questions,
  applicants,
  jobSaveId,
  emailRequired,
  phoneRequired,
  coverLetterRequired,
  resumeRequired,
  pointOfContact,
  id,
  description,
  pageUsername,
  ...item
}: IJobApi) => ({
  ...item,
  id: `${id}`,
  jobSaveId,
  languageId,
  title: {
    label: title,
    value: titleId,
  },
  description,
  pageUsername,
  experienceLevel: experienceLevels?.[experienceLevel],
  employmentType: employments?.[employmentType],
  salaryPeriod: salaryPeriods?.[salaryPeriod],
  workPlaceType: workPlaceTypes?.[workPlaceType],
  status: jobStats?.[status],
  isClosed: status === jobSelectedKeys.closed,
  isSaved: !!jobSaveId,
  salaryCurrency: salaryCurrencyId
    ? {
        value: salaryCurrencyId,
        label: `${salaryCurrencyCode} - ${salaryCurrencySymbol} - ${salaryCurrencyName}`,
        symbol: salaryCurrencySymbol,
        name: salaryCurrencyName,
        code: salaryCurrencyCode,
      }
    : undefined,
  skills: skills?.map((item) => {
    if (typeof item === 'string') return item;
    const { skillId, skillLevel, skillType, skillName } = item;
    const { progress, level } = skillNormalizer({ level: skillLevel });

    return {
      id: skillId,
      label: skillName,
      progress,
      level,
      skillLevel,
      type: skillType,
    };
  }),
  questions: questions?.map(({ questionType, ...rest }: any) => ({
    ...rest,
    questionType,
    // type: questionTypes[questionType],
  })),
  location: {
    lang: location?.lang,
    category: location?.category,
    value: location?.placeId,
    label: location?.name || location?.title,
    lat: location?.lat,
    lon: location?.lon,
    position: {
      lat: location?.lat,
      lon: location?.lon,
    },
  },
  languages: languages?.map(
    ({ languageId: lngId, languageLevel, languageName }: any) => {
      const { progress, level } = languageNormalizer({
        level: languageLevel,
      } as any);

      return {
        id: lngId,
        label: languageName,
        progress,
        level,
        languageLevel,
      };
    }
  ),
  category:
    categoryId || categoryName
      ? {
          value: categoryId,
          label: categoryName,
        }
      : undefined,
  applicants: applicants?.reduce((prev: Array<any>, curr: any) => {
    const normalizedItem = beforeCacheUserInfo(curr);

    // @ts-ignore
    return normalizedItem.hideIt ? prev : [...prev, normalizedItem];
  }, []),
  resumeRequired,
  emailRequired,
  phoneRequired,
  coverLetterRequired,
  pointOfContact: pointOfContact
    ? beforeCacheUserInfo(pointOfContact)
    : undefined,
});

const JobsListNormalizer = (
  data: PaginateResponse<IJobApi>
): PaginateResponse<IJob> => ({
  ...data,
  content: data.content.map(jobItemNormalizer),
  currentEntity: data?.currentEntity
    ? jobItemNormalizer(data.currentEntity)
    : null,
});

const JobDetailsNormalizer = (item: IJobApi): IJob => jobItemNormalizer(item);

const getOwners = (data: Array<any>): Array<Partial<UserType>> =>
  data?.map(({ user, ...rest }: any) => ({
    ...rest,
    croppedImageUrl: user.croppedImageUrl,
    usernameAtSign: `@${user.username}`,
    userId: user.id,
    fullName: `${user.name} ${user.surname}`,
    subTitle: `@${user.username}`,
  }));

const getCreator = (data: Record<string, any>): Partial<UserType> => {
  const { user, networkModel, occupationName } = data;

  return {
    // type: APP_ENTITIES.person,
    croppedImageUrl: user.croppedImageUrl,
    usernameAtSign: `@${user.username}`,
    id: user.id,
    fullName: `${user.name} ${user.surname}`,
    username: user.username,
    network: networkModel,
    occupation: { label: occupationName, value: undefined },
  };
};

const getJobCompanyInfo = (data: any): CompanyInfoType => {
  const pageDetail = beforeCachePageDetail(data);

  return {
    ...pageDetail,
    mutualConnectionsCount: Number(data?.mutualConnectionsCount),
    worksHereCount: Number(data?.worksHereCount),
    location: {
      label: data?.location?.name,
    },
    worksHere: (data.mutualWorksHere || []).reduce(
      (prev: Array<any>, curr: any) => {
        const normalizedItem = beforeCacheUserInfo(curr);

        // @ts-ignore
        return normalizedItem.hideIt ? prev : [...prev, normalizedItem];
      },
      []
    ),
    mutuals: data.mutualConnections.reduce((prev: Array<any>, curr: any) => {
      const normalizedItem = curr.page
        ? beforeCachePageDetail(curr.page)
        : beforeCacheUserInfo(curr.person);

      return normalizedItem.hideIt ? prev : [...prev, normalizedItem];
    }, []),
  };
};

export const searchJobsSuggest = (
  data: { id: string; title: string; type: string; imgUrl: string }[]
): { value: string; label: string; type: string; imgUrl: string }[] =>
  data?.map((item) => ({
    ...item,
    value: item?.id,
    label: item?.title,
    type: item?.type,
    imgUrl: item?.imgUrl,
  }));

export const getCandidacyLinkedJobs = (
  data: CandidacyLinkedJobsParamsType[]
): CandidacyLinkedJobsResponseType[] =>
  data?.map((item) => ({
    ...item.job,
    id: String(item.job.id),
  }));

export const candidateJobCardNormalizer = (
  data: PaginateResponse<CandidateProps>
): PaginateResponse<CandidateJobCardProps> => {
  const normalizedData = {
    ...data,
    content: data?.content?.map((item) => {
      if (!item?.candidate) {
        const user = item.applicant;

        return {
          ...item,
          id: user.id,
          fullName: user?.fullName,
          image: user?.croppedImageUrl,
          location: user?.location,
          occupation: user?.occupationName,
          username: user?.username,
        };
      }
      const user = item.candidate;

      return {
        ...item,
        id: user.id,
        fullName: user.profile?.fullName,
        image: user.profile?.croppedImageUrl,
        location: user?.profile?.location,
        occupation: user.profile?.occupationName,
        username: user.profile?.username,
      };
    }),
  };

  return normalizedData;
};

const jobsNormalizer = {
  getJobsList: JobsListNormalizer,
  getJobDetails: JobDetailsNormalizer,
  jobItem: jobItemNormalizer,
  getOwners,
  getCreator,
  getJobCompanyInfo,
  getCandidacyLinkedJobs,
  searchJobsSuggest,
};

export default jobsNormalizer;
