import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { APP_ENTITIES } from 'shared/utils/constants/app-entities';
import { db } from 'shared/utils/constants/enums';
import { locationZoomLevelValues } from 'shared/utils/constants/location';
import collectionToObjectByKey from '../toolkit/collectionToObjectByKey';
import type {
  BeforeCachePageDetailType,
  PageApiResponseType,
} from '@shared/types/page';

const pagesCategories = collectionToObjectByKey(db.CATEGORY_TYPES);
const companySizes = collectionToObjectByKey(db.COMPANY_SIZE_TYPES);

const beforeCachePageDetail = (
  pageData: PageApiResponseType
): BeforeCachePageDetailType => {
  const {
    username,
    category,
    companySize,
    industryName,
    industryLookupId,
    areaOfInterestLookupId,
    areaOfInterestName,
    locations = [],
    pageNetworkModel,
    title,
    ownerId,
    languageLookupId,
    languageName,
    headerImageLink,
    croppedHeaderImageLink,
    croppedHeaderImageData,
    imageUrl,
    croppedImageUrl,
    croppedImageData,
    croppedHeaderImageUrl,
    headerImageUrl,
    description,
    ...rest
  } = pageData;

  const { allLocations, primaryLocation } = locations?.reduce<any>(
    (prev, curr, currentIndex) => {
      const { lat, lon } = curr || {};
      const isDuplicate = prev.allLocations.some(
        (loc) => loc.location.lat === lat && loc.location.lon === lon
      );
      if (isDuplicate) {
        return prev;
      }
      const item = {
        name: curr.name,
        id: curr?.id,
        primaryLocation: curr.primaryLocation,
        location: {
          lat,
          lon,
          externalId: curr?.externalId,
          countryCode: curr?.countryCode,
          cityCode: curr?.cityCode,
          category: curr?.category,
          label: cleanRepeatedWords(curr?.title),
          title: cleanRepeatedWords(curr?.title),
          value: curr?.externalId,
          privacy: curr?.access,
          zoom: locationZoomLevelValues[curr?.category] || 10,
        },
      };

      return {
        primaryLocation:
          item.primaryLocation || currentIndex === 0
            ? item
            : prev.primaryLocation,
        allLocations: [...prev.allLocations, item],
      };
    },
    { allLocations: [], primaryLocation: {} }
  );

  return {
    ...rest,
    description: description || '',
    type: APP_ENTITIES.page,
    isPage: true,
    title,
    category: pagesCategories[category as any],
    companySize: companySizes[companySize as any],
    network: pageNetworkModel,
    username,
    fullName: title,
    usernameAtSign: `@${username}`,
    pageLink: `/${username}`,
    // establishmentDate: establishmentDate && formatDate(establishmentDate, 'YYYY'),
    industry: {
      label: industryName,
      value: industryLookupId,
    },
    language: {
      label: languageName,
      value: languageLookupId,
    },
    areaOfInterest: {
      label: areaOfInterestName,
      value: areaOfInterestLookupId,
    },
    locations: allLocations,
    location: primaryLocation?.location,
    ownerId,
    headerImageLink,
    croppedHeaderImageUrl,
    headerImageUrl,
    croppedHeaderImageLink,
    croppedHeaderImageData,
    imageUrl,
    croppedImageUrl,
    croppedImageData,
    profileImages: {
      headerImageLink: headerImageUrl,
      croppedHeaderImageUrl,
      headerImageUrl,
      croppedHeaderImageLink: croppedHeaderImageUrl,
      croppedHeaderImageData,
      imageUrl,
      croppedImageUrl,
      croppedImageData,
    },
  };
};

export default beforeCachePageDetail;
