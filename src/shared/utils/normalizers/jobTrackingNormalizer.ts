export default function jobTrackingNormalizer(data?: any) {
  if (!data) return {};

  const sortedStages = [...data.stages].sort(
    (a, b) => new Date(a.dateTime).getTime() - new Date(b.dateTime).getTime()
  );
  console.log({ data });

  return {
    ...data,
    job: {
      ...data.job,
      location: data.job.location.title,
      pageUsername: `@${data.job.pageUsername}`,
    },
    pointOfContact: {
      ...data?.pointOfContact,
      username: `@${data?.pointOfContact?.username}`,
      locationTitle: data?.pointOfContact?.location?.title,
    },
    stages: sortedStages,
  };
}
