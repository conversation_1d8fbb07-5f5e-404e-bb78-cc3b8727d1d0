import searchFiltersNormalizer from '@shared/utils/normalizers/searchFiltersNormalizer';
import {
  candidateEndpoints,
  jobsEndpoints,
  lookupEndpoints,
  pageEndpoints,
  projectsEndpoints,
} from 'shared/utils/constants/servicesEndpoints';
import { Endpoints } from '../constants';
import { normalizeCandidateReview } from '../normalizers/beforeCacheCandidateInfo';
import beforeCacheUserInfo from '../normalizers/beforeCacheUserInfo';
import { jobBillingNormalizer } from '../normalizers/billingsNormalizer';
import compareNormalizer from '../normalizers/compareNormalizer';
import { paginatedDataNormalizer } from '../normalizers/generalNormalizers';
import jobsNormalizer, {
  candidateJobCardNormalizer,
  jobItemNormalizer,
} from '../normalizers/jobs';
import jobTrackingNormalizer from '../normalizers/jobTrackingNormalizer';
import networkNormalizer from '../normalizers/network';
import { pipelinesNormalizer } from '../normalizers/pipelinesNormalizer';
import request from '../toolkit/request';
import { uploadFile as uploadFileRequest, type UploadFileProps } from './file';
import type { ReviewVisibilityType } from '../constants/enums/candidateDb';
import type { ActivityProps } from '@shared/types/activityProps';
import type {
  ApplicationProps,
  CandidateJobCardProps,
  CandidateProps,
  JobAPIProps,
  JobCollaboratorsProps,
  JobDataCreate,
  JobDataFormApplicationForm,
  JobDataFormBasicInfo,
  JobDataFormBenefits,
  JobDataFormGeneralInfo,
  JobDataFormManagement,
  JobDataFormPipeline,
  JobDataFormProjectsInfo,
  JobDataFormRequirements,
  JobProps,
  NormalizedJobParticipationModel,
} from '@shared/types/jobsProps';
import type { IPage } from '@shared/types/page';
import type { PaginateParamType } from '@shared/types/params';
import type { PeopleType } from '@shared/types/people';
import type { PriorityType } from '@shared/types/pipelineTypes';
import type { JobReviewProps, ReviewProps } from '@shared/types/review';
import type { UserApiResponse, UserType } from '@shared/types/user';
import type { colorsKeys } from '@shared/uikit/helpers/theme';
import type {
  IAppliedJobData,
  ICreateJobData,
  IJob,
  IJobApi,
  IResume,
  JobStatusType,
  CompanyInfoType,
  ISuggestSubmittedJob,
  IModifySelectedJobsByVendorRequestBody,
  IMatchingScore,
  BEJobBilling,
  IAutomationEnableData,
} from 'shared/types/job';
import type {
  JobTemplatesResponse,
  PaginateResponse,
} from 'shared/types/response';
import beforeCacheCandidateInfo from '../normalizers/beforeCacheCandidateInfo';

export const createJob = async (jobData: ICreateJobData): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.createJob, jobData);

  return data;
};
export const postApplication = async (
  jobData: IAppliedJobData
): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.postApplication, jobData);

  return { ...data, followPage: jobData.followPage };
};

export const postPipelineAutomationEnabled = async (
  body: IAutomationEnableData
): Promise<any> => {
  const { id, ...restBody } = body;
  const { data } = await request.post(
    jobsEndpoints.postPipelineAutomationEnabled(id),
    restBody
  );

  return data;
};

export const updateJob = async (
  jobData: ICreateJobData & { id: string }
): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.getBusinessJobDetails(jobData.id),
    jobData
  );

  return data;
};

export const getJobsList = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getJobsList,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getJobDetails = async ({
  params,
  headers,
}: any): Promise<IJob> => {
  if (!params.id) {
    throw new Error('To get job details id is required.');
  }
  const url = params?.isBusinessApp
    ? jobsEndpoints.getBusinessJobDetails
    : jobsEndpoints.getUserJobDetails;
  const { data } = await request.get<IJobApi>(url(params?.id), { headers });

  return jobsNormalizer.getJobDetails(data);
};

export const getJobCreatorDetail = async ({
  params,
}: {
  params: { id: string };
}): Promise<Partial<UserType>> => {
  const url = jobsEndpoints.getJobCreatorDetail(params?.id);
  const { data } = await request.get(url);

  return jobsNormalizer.getCreator(data);
};

export const deleteJob = async ({ id }: { id: string }): Promise<any> => {
  const url = jobsEndpoints.getBusinessJobDetails(id);
  const { data } = await request.delete(url);

  return data;
};

export const setStatus = async (jobData: {
  id: string;
  status: JobStatusType;
}): Promise<any> => {
  const { status, id } = jobData;
  const { data } = await request.put(jobsEndpoints.updateStatus(id), {
    status,
  });

  return data;
};

export const setPriority = async (jobData: {
  id: string;
  priority: PriorityType;
}): Promise<any> => {
  const { priority, id } = jobData;
  const { data } = await request.put(jobsEndpoints.updatePriority(id), {
    priority,
  });

  return data;
};

export const withdraw = async (jobData: { id: string }): Promise<any> => {
  const { id } = jobData;
  const { data } = await request.post(jobsEndpoints.withdraw(id), {});

  return data;
};

export const saveJob = async (jobData: { id: string }): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.saveJob, {
    jobId: jobData.id,
  });

  return data;
};

export const unSaveJob = async (jobData: { id: string }): Promise<any> => {
  const { data } = await request.delete(jobsEndpoints.unSaveJob(jobData?.id));

  return data;
};

export const checkHasJobs = async (): Promise<boolean> => {
  const { data } = await request.get<boolean>(jobsEndpoints.availabilitycheck);

  return data;
};

export const userSavedJobsAvailable = async (): Promise<boolean> => {
  const { data } = await request.get<boolean>(
    jobsEndpoints.userSavedJobsAvailable
  );

  return data;
};

export const getPagePublishedJobs = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getPagePublishedJobs,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};
export const getJobsApplicationList = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getJobsApplicationList,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getJobsSavedList = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getJobsSavedList,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getJobsTopSuggestionList = async ({
  params,
  headers,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getJobsTopSuggestionList,
    {
      headers,
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getAllJobs = async ({
  params,
  headers,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getAllJobs,
    {
      headers,
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getJobsPopularList = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.getJobsPopularList,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

export const getJobsRecentSearchList = async ({
  params,
}: {
  params: {
    page: number;
    size: number;
  };
}): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<any>>(
    jobsEndpoints.getJobsRecentSearchList,
    {
      params,
    }
  );

  return data;
};

export const getSearchJobsSuggestions = async ({
  params,
}: {
  params: {
    page: number;
    size: number;
  };
}): Promise<PaginateResponse<IJob>> => {
  const { data } = await request.get<PaginateResponse<any>>(
    jobsEndpoints.getSearchJobsSuggestions,
    {
      params,
    }
  );

  return data;
};

const getResume = async ({ params = {} }: any): Promise<any> => {
  const { userId } = params;
  const { data } = await request.get<any>(jobsEndpoints.getResume(userId));

  return data;
};

export const deleteResume = async ({
  id,
}: {
  id: string;
}): Promise<IResume> => {
  const url = jobsEndpoints.deleteResume(id);
  const { data } = await request.delete(url);

  return data;
};

export const removeRecentSearch = async ({
  id,
}: {
  id: string;
}): Promise<any> => {
  const url = jobsEndpoints.recentSearch(id);
  const { data } = await request.delete(url);

  return data;
};

export const removeAllRecentSearch = async (): Promise<any> => {
  const url = jobsEndpoints.getJobsRecentSearchList;
  const { data } = await request.delete(url);

  return data;
};

const getJobTemplates = async ({
  params,
  headers,
}: any): Promise<JobTemplatesResponse<any>> => {
  const { data } = await request.get<JobTemplatesResponse<any>>(
    jobsEndpoints.getJobTemplates,
    {
      params,
      headers,
    }
  );

  return data;
};
const getJobTemplatesGeneral = async ({
  params,
  headers,
}: any): Promise<JobTemplatesResponse<any>> => {
  const { data } = await request.get<JobTemplatesResponse<any>>(
    jobsEndpoints.getJobTemplatesGeneral,
    {
      params,
      headers,
    }
  );

  return data;
};

const getJobTemplatesPersonal = async ({
  params,
  headers,
}: any): Promise<JobTemplatesResponse<any>> => {
  const { data } = await request.get<JobTemplatesResponse<any>>(
    jobsEndpoints.getJobTemplatesPersonal,
    {
      params,
      headers,
    }
  );

  return data;
};

const getPopularCategories = async ({ params, headers }: any): Promise<any> => {
  const { data } = await request.get<PaginateResponse<any>>(
    lookupEndpoints.popularJobsCategories,
    {
      params,
      headers,
    }
  );

  return data;
};

const subCategoryJobList = async ({
  params,
}: {
  params: {
    categoryId: string;
    page: number;
    size: number;
  };
}): Promise<PaginateResponse<any>> => {
  const { data } = await request.get<PaginateResponse<any>>(
    jobsEndpoints.subCategoryJobList,
    {
      params,
    }
  );

  return jobsNormalizer.getJobsList(data);
};

const getOwnersLis = async ({
  params,
}: any): Promise<Array<Partial<UserType>>> => {
  const { data } = await request.get<Array<any>>(
    jobsEndpoints.getJobOwnersLis(params?.id)
  );

  return jobsNormalizer.getOwners(data);
};

export const postOwner = async (jobData: {
  userId: string;
  jobId: string;
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.postJobOwner(jobData.jobId),
    {
      userId: jobData.userId,
    }
  );

  return data;
};

export const removeOwner = async (jobData: {
  userId: string;
  jobId: string;
}): Promise<any> => {
  const url = jobsEndpoints.postJobOwner(jobData.jobId);
  const { data } = await request.delete(url, { userId: jobData.userId });

  return data;
};

export const searchJob = async ({
  params,
}: any): Promise<PaginateResponse<IJob>> => {
  const url = params?.isBusinessApp
    ? jobsEndpoints.searchBusinessJobs
    : jobsEndpoints.searchUserJobs;

  const { data } = await request.get<PaginateResponse<IJobApi>>(url, {
    params,
  });

  return params?.isBusinessApp ? data : jobsNormalizer.getJobsList(data);
};
export const searchJobsSuggest = async ({ params }: any): Promise<any[]> => {
  const url = jobsEndpoints.searchJobsSuggest;

  const { data } = await request.get<any[]>(url, {
    params,
  });

  return jobsNormalizer.searchJobsSuggest(data);
};

export const getSearchFilters = async ({ params }: any): Promise<any> => {
  const url = params?.isBusinessApp
    ? jobsEndpoints.getSearchBusinessFilters
    : jobsEndpoints.getSearchUserFilters;
  const { data } = await request.get<any>(url, {
    params,
  });

  return params?.isBusinessApp
    ? searchFiltersNormalizer.recruiterJobsFilters(data)
    : searchFiltersNormalizer.jobsFilters(data);
};

export const getJobCompanyInfo = async ({
  params,
}: any): Promise<CompanyInfoType> => {
  if (!params.pageId) {
    throw new Error('To get company info  id is required.');
  }

  const { data } = await request.get<any>(
    pageEndpoints.getJobCompanyInfo(params.pageId)
  );

  return data;
};

export const getCompanyFollowerPerson = async (
  params: PaginateParamType = {}
): Promise<PaginateResponse<PeopleType>> => {
  const { data } = await request.get(jobsEndpoints.getCompanyFollowerPerson, {
    params,
  });

  return networkNormalizer.suggestPeople(data);
};

export const getCompanyFollowerPage = async (
  params: PaginateParamType = {}
): Promise<PaginateResponse<IPage>> => {
  const { data } = await request.get(jobsEndpoints.getCompanyFollowerPage, {
    params,
  });

  return networkNormalizer.suggestPages(data);
};

export const reportJob = async ({
  jobId,
  title,
  description,
}: {
  jobId: string;
  title: string;
  description: string;
}): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.reportJob, {
    jobId,
    title,
    description,
  });

  return data;
};

const shareResume = async (params: {
  fileId: number;
  userIds: number[];
}): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.shareResume, params);

  return data;
};

export const getJobsByIds = async ({
  params,
}: {
  params: {
    ids: Array<string>;
  };
}): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.getJobsByIds, {
    params,
  });

  return data?.map(jobItemNormalizer);
};
export const getJobsForLinking = async ({
  params,
}: {
  params: {
    text: string;
    page: number;
    size?: number;
    status?: JobStatusType;
  };
}): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.searchBusinessJobs, {
    params,
  });

  return data;
};
export const getJobApplications = async ({
  params,
}: {
  params: {
    id: string;
    size: number;
    page?: number;
  };
}): Promise<PaginateResponse<ApplicationProps>> => {
  const { data } = await request.get<PaginateResponse<ApplicationProps>>(
    Endpoints.App.Job.getApplication(params.id),
    {
      params: {
        page: params.page ?? 0,
        size: params.size,
      },
    }
  );

  return candidateJobCardNormalizer(data);
};
export const getJobCandidates = async ({
  params,
}: {
  params: {
    id: string;
    size: number;
    page?: number;
  };
}): Promise<PaginateResponse<CandidateJobCardProps>> => {
  const { data } = await request.get<PaginateResponse<CandidateProps>>(
    Endpoints.App.Job.getCandidacy(params.id),
    {
      params: {
        page: params.page ?? 0,
        size: params.size,
      },
    }
  );

  return candidateJobCardNormalizer(data);
};
export const getJobReviews = async (params: {
  participationId: string;
  page: number;
  size: number;
}): Promise<PaginateResponse<ReviewProps>> => {
  const { data } = await request.get<PaginateResponse<ReviewProps>>(
    Endpoints.App.Job.getReviews(params.participationId),
    {
      params: {
        page: params.page ?? 0,
        size: params.size,
      },
    }
  );

  return data;
};

export const getUserApplications = async (
  userId: string
): Promise<Array<ApplicationProps & { job: JobProps }>> => {
  const { data } = await request.get<
    Array<ApplicationProps & { job: JobProps }>
  >(jobsEndpoints.getUserApplications(userId));

  return data;
};

export const getCandidacyLinkedJobs = async (
  candidateId: string
): Promise<any> => {
  const { data } = await request.get<
    Array<ApplicationProps & { job: JobProps }>
  >(jobsEndpoints.getCandidacyLinkedJobs(candidateId));

  return jobsNormalizer.getCandidacyLinkedJobs(data);
};

export const getCandidateActivities = async (
  candidateId: string
): Promise<any> => {
  const { data } = await request.get<PaginateResponse<ActivityProps>>(
    Endpoints.App.Job.getCandidateActivities(candidateId)
  );

  return data;
};

export const getCollaborators = async (jobId: string): Promise<any> => {
  const { data } = await request.get<JobCollaboratorsProps>(
    Endpoints.App.Job.getCollaborators(jobId)
  );

  return {
    pointOfContact: data?.pointOfContact
      ? beforeCacheUserInfo(data?.pointOfContact as any)
      : undefined,
    collaborators: data?.collaborators
      ? data?.collaborators.map((collab) => beforeCacheUserInfo(collab as any))
      : [],
  };
};

export const uploadFile = ({
  file,
  onProgress,
  ...rest
}: UploadFileProps): Promise<any> =>
  uploadFileRequest({
    url: jobsEndpoints.fileUpload,
    file,
    onProgress,
    ...rest,
  });
export const updateJobAssignees = async (
  jobId: string,
  userIds: string[]
): Promise<any> => {
  const { data } = await request.put<any>(
    Endpoints.App.Job.collaborators(jobId),
    { userIds }
  );

  return data;
};

export const updateBulkCandidate = async (params: {
  jobIds: number[];
  candidateIds: number[];
}): Promise<any> => {
  const { data } = await request.put<any>(
    Endpoints.App.Job.updateBulkCandidate,
    params
  );

  return data;
};

export const updateJobTags = async (
  jobId: string,
  tags: string[]
): Promise<any> => {
  const { data } = await request.put<any>(Endpoints.App.Job.tags(jobId), {
    tags,
  });

  return data;
};

export const getPreviousCL = async (): Promise<any> =>
  request.get<JobCollaboratorsProps>(Endpoints.App.Job.previousCL);

export const addCandidate = async (params: {
  jobId: string;
  candidateIds: string[];
}): Promise<any> => request.put(Endpoints.App.Job.addCandidate, params);
export const getAllCandidates = async (jobId: string): Promise<any> => {
  const { data } = await request.get(
    Endpoints.App.Job.getAllJobCandidacy(jobId)
  );

  return {
    ids: data?.content.map((id: string) => ({ id })),
    jobId,
  };
};
export const getBusinessJobFilters = async (params: {
  searchId?: string;
}): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.getSearchBusinessFilters, {
    params,
  });

  return { data };
};
export const getBusinessJobs = async (params: any): Promise<any> => {
  const { data } = await request.get<PaginateResponse<JobAPIProps>>(
    jobsEndpoints.searchBusinessJobs,
    {
      params,
    }
  );

  return data;
};
export const getPipeline = async (pipelineId: string): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.getPipeline(pipelineId));

  return { data };
};
export const deletePipeline = async (pipelineId: string): Promise<any> => {
  const { data } = await request.delete(jobsEndpoints.getPipeline(pipelineId));

  return { data };
};
export const getPipelineParticipations = async ({
  params,
  id,
}: {
  id: string;
  params: {
    page: number;
  };
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.getPipelineParticipants(id),
    {
      params: {
        ...params,
        size: 50,
      },
    }
  );

  return { ...data, content: pipelinesNormalizer(data.content) };
};
export const updateUserPipeline = async ({
  pipelineId,
  userId,
}: {
  userId: string;
  pipelineId: string;
}): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.moveUserPipeline(userId), {
    pipelineId,
  });

  return { data };
};

export const getJobsAllCandidateIds = async (params: {
  jobIds: string[];
}): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.getJobsAllCandidateIds, {
    params,
  });

  return data;
};

export const batchAddCandidatesToJobs = async (params: {
  jobIds: string[];
  candidateIds: string[];
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.batchAddCandidatesToJobs,
    params
  );

  return data;
};

export const batchUpdateStatus = async (params: {
  ids: string[];
  status: JobStatusType;
}): Promise<any> => {
  const { data } = await request.put(jobsEndpoints.batchUpdateStatus, params);

  return data;
};

export const batchUpdatePriority = async (params: {
  ids: string[];
  priority: PriorityType;
}): Promise<any> => {
  const { data } = await request.put(jobsEndpoints.batchUpdatePriority, params);

  return data;
};
export const updateJobProjectInfo = async (
  jobData: JobDataFormProjectsInfo
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    projectsEndpoints.updateLinkedProjects(jobData.id),
    rest
  );

  return data;
};
export const updateJobBasicInfo = async (
  jobData: JobDataFormBasicInfo
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateBasicInfo(jobData.id ?? ''),
    rest
  );

  return data;
};
export const updateJobGeneralInfo = async (
  jobData: JobDataFormGeneralInfo
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateGeneralInfo(jobData.id),
    rest
  );

  return data;
};
export const updateJobManagement = async (
  jobData: JobDataFormManagement
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateManagement(jobData.id),
    rest
  );

  return data;
};
export const updateJobApplicationForm = async (
  jobData: JobDataFormApplicationForm
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateApplicationForm(jobData.id),
    rest
  );

  return data;
};
export const updateJobRequirements = async (
  jobData: JobDataFormRequirements
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateRequirements(jobData.id),
    rest
  );

  return data;
};
export const updateJobBenefits = async (
  jobData: JobDataFormBenefits
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updateBenefits(jobData.id),
    rest
  );

  return data;
};
export const updateJobPipelines = async (
  jobData: JobDataFormPipeline
): Promise<any> => {
  const { id, ...rest } = jobData;
  const { data } = await request.put(
    jobsEndpoints.updatePipelines(jobData.id),
    rest
  );

  return data;
};
export const createBusinessJob = async (
  jobData: JobDataCreate
): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.createJob, jobData);

  return data;
};
export const getBusinessJobDetails = async (jobId: string): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.getBusinessJobDetails(jobId)
  );

  return data;
};
export const ChangePipelineApplicantTrack = async (params: {
  id: string;
  applicantTrack: boolean;
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.changePipelineApplicantTrack(params.id),
    {
      applicantTrack: params.applicantTrack,
    }
  );

  return data;
};
export const ChangePipelineColor = async (params: {
  id: string;
  color: colorsKeys;
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.changePipelineColor(params.id),
    {
      color: params.color,
    }
  );

  return data;
};

export const getTrackingJob = async (jobId: string): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.getTrackingJob(jobId));

  return jobTrackingNormalizer(data);
};

export const getJobTrackingMeetings = async (
  participationId: string
): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.getTrackingJobMeetingsList(participationId)
  );

  return data;
};

export const pinCandidate = async (params: {
  id: string;
  pin: boolean;
}): Promise<any> => {
  const { data } = await request.put(jobsEndpoints.pinCandidate(params.id), {
    pin: params.pin,
  });

  return data;
};

export const countUserParticipation = async (params: {
  id: string;
}): Promise<{ value: string }> => {
  const { data } = await request.get(
    jobsEndpoints.countUserParticipations(params.id)
  );

  return data;
};

export const searchPipelineParticipations = async ({
  params,
  id,
  ids,
}: {
  id?: string;
  params: {
    page: number;
  };
  ids?: string[];
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.searchPipelineParticipations,
    {
      params: {
        ...params,
        size: 50,
        pipelineIds: ids ?? [id],
      },
    }
  );

  return { ...data, content: pipelinesNormalizer(data.content) };
};
export const batchMoveCandidatesToStage = async (params: {
  pipelineId: string;
  participationIds: string[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.batchMoveCandidatesToStage,
    {
      ...params,
    }
  );

  return data;
};
export const batchAddNoteToCandidates = async (params: {
  participationIds: string[];
  body: string;
  fileIds: string[];
  visibility: 'TEAM' | 'ONLY_ME';
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.batchAddNoteToCandidates,
    params
  );

  return data;
};
export const batchAddTodoToCandidates = async (params: {
  participationIds: string[];
  assigneeUserId: number;
  title: string;
  description: string;
  status: 'OPEN' | 'ON_HOLD' | 'DONE';
  start?: string;
  end?: string;
  fileIds: string[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.batchAddTodoToCandidates,
    params
  );

  return data;
};
export const compareUsers = async ({
  users,
}: {
  users: NormalizedJobParticipationModel[];
}): Promise<any> => {
  const data = await Promise.all(
    users.map(async (user) => {
      if (user.type === 'CANDIDATE') {
        const { data: candidate } = await request.get(
          candidateEndpoints.singleCandidateById(
            user?.candidate?.id || user?.id
          )
        );

        return candidate;
      }
      const { data: applicant } = await request.get(
        Endpoints.App.User.Profile.get,
        {
          params: {
            username: user.username,
          },
        }
      );

      return applicant;
    })
  );

  return compareNormalizer(data);
};

export const getJobTrackingEmails = async (
  participationId: string
): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.getJobTrackingEmails(participationId)
  );

  return data;
};

export const getMatchingScore = async ({
  params,
}: {
  params: string;
}): Promise<IMatchingScore> => {
  const { data } = await request.get(jobsEndpoints.getMatchingScore(params));

  return data;
};

export const getJobBillingList = async (params: PaginateParamType) => {
  const { data } = await request.get<PaginateResponse<BEJobBilling>>(
    jobsEndpoints.getJobBillingList,
    {
      params,
    }
  );

  return paginatedDataNormalizer(data, jobBillingNormalizer);
};

export const searchBusinessJob = async ({
  params,
}: any): Promise<PaginateResponse<IJobApi>> => {
  const { data } = await request.get<PaginateResponse<IJobApi>>(
    jobsEndpoints.searchBusinessJobs,
    {
      params,
    }
  );

  return data;
};

export const suggestSubmittedJob = async ({
  params,
}: {
  params: {
    clientId: number;
    text: string;
    candidateId: number;
    excludedIds: number[];
    vendorId?: number;
  };
}): Promise<ISuggestSubmittedJob[]> => {
  const { data } = await request.get(jobsEndpoints.suggestSubmittedJob, {
    params,
  });

  return data;
};

export const modifySelectedJobsByVendor = async (
  body: IModifySelectedJobsByVendorRequestBody
) => {
  const { data } = await request.post(
    jobsEndpoints.modifySelectedJobsByVendor,
    body
  );

  return data;
};

export interface AutoMovementRequest {
  toPipelineId: number;
  conditionMatchMode: 'ALL' | 'ANY' | string;
  coverLetterCheckingEnabled: boolean;
  coverLetterPresentedOrNot: boolean;
  phoneNumberCheckingEnabled: boolean;
  phoneNumberPresentedOrNot: boolean;
  ageRangeCheckingEnabled: boolean;
  ageRangeIsBetweenOrNot: boolean;
  minAge: number;
  maxAge: number;
  locationCheckingEnabled: boolean;
  locationCountriesContainsOrNot: boolean;
  locationCountriesIs: {
    id: number;
    name: string;
    code: string;
  }[];
  templateId: number;
}

export interface AutoMovementResponse {
  to: {
    id: number;
    title: string;
    type: 'REVIEW' | string;
    stageType: 'HIRING' | string;
    applicantTrack: boolean;
    order: number;
    color: string;
    count: number;
    autoMovementEnabled: boolean;
    autoRejectionEnabled: boolean;
    autoNoteEnabled: boolean;
    autoTodoEnabled: boolean;
    autoReplyEnabled: boolean;
    autoInterviewEnabled: boolean;
    autoAssessmentEnabled: boolean;
    assessmentId: number;
    jobId: number;
    jobTitle: string;
  };
  conditionMatchMode: 'ALL' | 'ANY' | string;
  coverLetterCheckingEnabled: boolean;
  coverLetterPresentedOrNot: boolean;
  phoneNumberCheckingEnabled: boolean;
  phoneNumberPresentedOrNot: boolean;
  ageRangeCheckingEnabled: boolean;
  ageRangeIsBetweenOrNot: boolean;
  minAge: number;
  maxAge: number;
  locationCheckingEnabled: boolean;
  locationCountriesContainsOrNot: boolean;
  locationCountries: {
    id: number;
    name: string;
    code: string;
  }[];
  templateId?: number;
}

export const pipelineBatchReject = async (params: {
  participationIds: string[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBatchReject,
    params
  );

  return data;
};

export const getSkillboardScore = async (params: {
  participationId: string;
  candidateId?: string;
  isCandidateMode?: boolean;
}): Promise<any> => {
  const { data } = await request.get(
    params.isCandidateMode
      ? candidateEndpoints.getCandidateSkillboard(params?.candidateId)
      : jobsEndpoints.getParticipationSkillboard(params?.participationId)
  );

  return data;
};

export const addSkillboardScoreCandidate = async (params: {
  participationId: string;
  candidateId?: string;
  skill: string;
  score?: number;
}): Promise<any> => {
  const { data } = await request.post(
    candidateEndpoints.addCandidateSkillItem(params?.candidateId),
    {
      skill: params.skill,
      score: params?.score,
    }
  );

  return data;
};

export const addSkillboardScoreParticipation = async (params: {
  participationId: string;
  candidateId?: string;
  skill: string;
  score?: number;
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.addParticipationSkillItem(params.participationId),
    {
      skill: params.skill,
      score: params?.score,
    }
  );

  return data;
};

export const updateSkillboardScoreCandidate = async (params: {
  skillId: string;
  skill: string;
  score?: number;
}): Promise<any> => {
  const { data } = await request.put(
    candidateEndpoints.editCandidateSkill(params.skillId),
    {
      skill: params.skill,
      score: params?.score || null,
    }
  );

  return data;
};

export const updateSkillboardScoreParticipation = async (params: {
  skillId: string;
  skill: string;
  score?: number;
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.editParticipationSkill(params.skillId),
    {
      skill: params.skill,
      score: params?.score || null,
    }
  );

  return data;
};

export const deleteSkillboardScoreCandidate = async (params: {
  skillId?: string;
}): Promise<any> => {
  const { data } = await request.delete(
    candidateEndpoints.removeCandidateSkill(params?.skillId)
  );

  return data;
};

export const deleteSkillboardScoreParticipation = async (params: {
  skillId?: string;
}): Promise<any> => {
  const { data } = await request.delete(
    jobsEndpoints.removeParticipationSkill(params?.skillId)
  );

  return data;
};

export const setSkillboardScoreCandidate = async (params: {
  skillId: string;
  score: number;
}): Promise<any> => {
  const { data } = await request.put(
    candidateEndpoints.setSkillboardScore(params.skillId),
    {
      score: params.score,
    }
  );

  return data;
};
export const setSkillboardScoreParticipation = async (params: {
  skillId: string;
  score: number;
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.setParticipationSkill(params.skillId),
    {
      score: params.score,
    }
  );

  return data;
};

export const batchSetSkillboardScoreCandidate = async (
  params: {
    id: string;
    score: number;
  }[]
): Promise<any> => {
  const { data } = await request.post(
    candidateEndpoints.batchSetSkillboardScoreCandidate,
    {
      items: params,
    }
  );

  return data;
};
export const batchSetSkillboardScoreParticipation = async (
  params: {
    id: string;
    score: number;
  }[]
): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.batchSetSkillboardScore, {
    items: params,
  });

  return data;
};

export const pipelineBatchMessage = async (params: {
  participationIds: string[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBatchMessage,
    params
  );

  return data;
};

export const withdrawDuplicateCandidate = async (params: {
  jobId: number;
  candidateIds: number[];
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.withdrawDuplicateCandidate,
    params
  );

  return data;
};

export const pipelineBatchMeeting = async (params: {
  participationIds: string[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBatchMeeting,
    params
  );

  return data;
};

export const pipelineBatchEmail = async (params: {
  participationIds: string[];
}): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.pipelineBatchEmail, params);

  return data;
};

export const submitCandidateByVendor = async (params: {
  jobId: string;
  candidateIds: Array<string>;
}): Promise<any> => {
  const { data } = await request.put(
    jobsEndpoints.submitCandidateByVendor,
    params
  );

  return data;
};

export const getCandidaciesOfProject = async (params: {
  id: string;
  number: number;
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.candidaciesOfProject(params.id),
    { params: { number: params.number } }
  );

  return beforeCacheCandidateInfo(data);
};

export const getApplicationsOfProject = async (params: {
  id: string;
  number: number;
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.applicationsOfProject(params.id),
    { params: { number: params.number } }
  );

  return beforeCacheCandidateInfo(data);
};

export const getCandidaciesOfJob = async (params: {
  id: string;
  number: number;
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.candidaciesOfJob(params.id),
    { params: { number: params.number } }
  );

  return beforeCacheCandidateInfo(data);
};

export const getApplicationsOfJob = async (params: {
  id: string;
  number: number;
}): Promise<any> => {
  const { data } = await request.get(
    jobsEndpoints.applicationsOfJob(params.id),
    { params: { number: params.number } }
  );

  return beforeCacheCandidateInfo(data);
};

export const searchParticipationItem = async (params: {
  number: number;
  id: number[];
}): Promise<any> => {
  const { data } = await request.get(jobsEndpoints.searchParticipationItem, {
    params: {
      number: params.number,
      pipelineIds: [params.id],
    },
  });

  return beforeCacheCandidateInfo(data);
};
export const getReviewsOfJob = async (params: {
  jobId: string;
  page: number;
  size: number;
}): Promise<PaginateResponse<JobReviewProps>> => {
  const { jobId, ...otherParams } = params;
  const { data } = await request.get(jobsEndpoints.getReviewsOfJob(jobId), {
    params: otherParams,
  });

  const { content, ...rest } = data;

  return {
    ...rest,
    content: content.map(normalizeCandidateReview),
  };
};

export const pipelineBulkActionMoveTo = async (params: {
  pipelineId: string;
  participationIds: number[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBulkActionMoveTo,
    params
  );

  return data;
};

export const pipelineBulkActionMeeting = async (params: {
  timesheetId: number;
  templateId: number;
  participationIds: number[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBulkActionMeeting,
    params
  );

  return data;
};

export const pipelineBulkActionEmail = async (params: {
  templateId: number;
  participationIds: number[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBulkActionEmail,
    params
  );

  return data;
};

export const pipelineBulkActionNote = async (params: {
  visibility: ReviewVisibilityType;
  body: string;
  allTeamMembersTagged: boolean;
  taggedUserIds: number[];
  fileIds: number[];
  participationIds: number[];
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBulkActionNote,
    params
  );

  return data;
};

export const pipelineBulkActionTodo = async (params: {
  title: string;
  description: string;
  assigneeUserId: number;
  allTeamMembersTagged: boolean;
  taggedUserIds: number;
  start: string;
  end: string;
  remind: boolean;
}): Promise<any> => {
  const { data } = await request.post(
    jobsEndpoints.pipelineBulkActionTodo,
    params
  );

  return data;
};

export const getActivitiesOfVendorClients = async (
  params: any
): Promise<any> => {
  const { data } = await request.get<PaginateResponse<JobAPIProps>>(
    jobsEndpoints.getActivitiesOfVendorClients(params.vendorClientId),
    {
      params,
    }
  );

  return data;
};

export const searchParticipationAsApplicant = async (
  params: any
): Promise<PaginateResponse<UserApiResponse>> => {
  const { data } = await request.get<PaginateResponse<UserApiResponse>>(
    jobsEndpoints.searchParticipationAsApplicant,
    {
      params,
    }
  );

  return {
    ...data,
    content: data?.content?.map(beforeCacheUserInfo),
  };
};

export const searchParticipationAsCandidate = async (
  params: any
): Promise<PaginateResponse<UserApiResponse>> => {
  const { data } = await request.get<PaginateResponse<UserApiResponse>>(
    jobsEndpoints.searchParticipationAsCandidate,
    {
      params,
    }
  );

  return {
    ...data,
    content: data?.content?.map(beforeCacheUserInfo),
  };
};

export const searchReviews = async (
  params: any
): Promise<PaginateResponse<JobReviewProps>> => {
  const { data } = await request.get<PaginateResponse<JobReviewProps>>(
    jobsEndpoints.searchReviews,
    {
      params,
    }
  );

  const { content, ...rest } = data;

  return {
    ...rest,
    content: content.map(normalizeCandidateReview),
  };
};

export const getParticipationFilters = async ({
  params,
}: {
  params: any;
}): Promise<any> => {
  const { data } = await request.get<any>(
    jobsEndpoints.getParticipationFilters,
    {
      params,
    }
  );
  return data;
};

export const getReviewFilters = async ({
  params,
}: {
  params: { searchId: string };
}): Promise<any> => {
  const { data } = await request.get<any>(jobsEndpoints.getReviewFilters, {
    params,
  });
  return data;
};

export const postReject = async (body: {
  participationId: number;
  templateId: number;
  title: string;
  subject: string;
  message: string;
  fileIds: number[];
  automated: boolean;
}) => {
  const { data } = await request.post(jobsEndpoints.reject, body);

  return data;
};

export default {
  getJobTrackingEmails,
  getJobTemplates,
  getJobTemplatesGeneral,
  getJobTemplatesPersonal,
  postApplication,
  createJob,
  getJobsList,
  getJobDetails,
  updateJob,
  deleteJob,
  setStatus,
  checkHasJobs,
  getJobsApplicationList,
  getJobsTopSuggestionList,
  getJobsPopularList,
  getJobsRecentSearchList,
  getJobsSavedList,
  withdraw,
  saveJob,
  unSaveJob,
  getPopularCategories,
  subCategoryJobList,
  getResume,
  shareResume,
  deleteResume,
  removeAllRecentSearch,
  removeRecentSearch,
  getOwnersLis,
  postOwner,
  removeOwner,
  getJobCreatorDetail,
  userSavedJobsAvailable,
  searchJob,
  getSearchFilters,
  getJobCompanyInfo,
  getCompanyFollowerPerson,
  getCompanyFollowerPage,
  getAllJobs,
  reportJob,
  getSearchJobsSuggestions,
  searchJobsSuggest,
  getJobsByIds,
  getPagePublishedJobs,
  getJobApplications,
  getJobCandidates,
  getJobReviews,
  setPriority,
  getUserApplications,
  getCandidacyLinkedJobs,
  getCandidateActivities,
  getCollaborators,
  uploadFile,
  getPreviousCL,
  getMatchingScore,
  pipelineBatchReject,
  getSkillboardScore,
  addSkillboardScoreCandidate,
  addSkillboardScoreParticipation,
  updateSkillboardScoreCandidate,
  updateSkillboardScoreParticipation,
  deleteSkillboardScoreCandidate,
  deleteSkillboardScoreParticipation,
  setSkillboardScoreCandidate,
  setSkillboardScoreParticipation,
  batchSetSkillboardScoreCandidate,
  batchSetSkillboardScoreParticipation,
  pipelineBatchMessage,
  pipelineBatchMeeting,
  postReject,
  pipelineBatchEmail,
  submitCandidateByVendor,
  getBusinessJobs,
  getReviewsOfJob,
  getActivitiesOfVendorClients,
  searchParticipationAsApplicant,
  searchParticipationAsCandidate,
  searchReviews,
  getBusinessJobFilters,
  getParticipationFilters,
  getReviewFilters,
};
