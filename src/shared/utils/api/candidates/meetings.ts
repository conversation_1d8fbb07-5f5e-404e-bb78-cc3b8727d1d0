import { normalizeCandidateMeeting } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import {
  candidateEndpoints,
  jobsEndpoints,
  schedulesSearchEndpoints,
} from '../../constants/servicesEndpoints';
import request from '../../toolkit/request';
import type {
  BECandidateMeeting,
  CandidateMeetingRequest,
  ICandidateMeeting,
} from '@shared/types/candidates';
import type { ICreateMeetingData } from '@shared/types/schedules/schedules';
import schedulesSearch from '@shared/utils/constants/servicesEndpoints/services/schedulesSearch';
import { PaginateResponse } from '@shared/types/response';

export const getCandidatesMeetings = async (params: {
  candidateId?: string | number;
  page?: number;
  size?: number;
  onlyDone?: boolean;
}): Promise<Array<ICandidateMeeting>> => {
  const { data } = await request.get<Array<BECandidateMeeting>>(
    candidateEndpoints.meetings,
    params
  );

  return data.map(normalizeCandidateMeeting);
};

export const addCandidateMeeting = async (
  candidateId: string,
  body: CandidateMeetingRequest
): Promise<BECandidateMeeting> => {
  const url = candidateEndpoints.singleMeetingById(candidateId);
  const { data } = await request.post<BECandidateMeeting>(url, body);

  return data;
};

export const editCandidateMeeting = async (
  meetingId: string,
  body: CandidateMeetingRequest,
  isCandidateMode: boolean
) => {
  const url = isCandidateMode
    ? candidateEndpoints.createCandidateMeeting(meetingId)
    : jobsEndpoints.createCandidateMeeting(meetingId);
  return request.put<BECandidateMeeting>(url, body);
};

export const removeCandidateMeeting = async (params: {
  eventId: string;
  isCandidateMode: boolean;
}): Promise<BECandidateMeeting> => {
  const url = params.isCandidateMode
    ? candidateEndpoints.singleMeetingById(params?.eventId)
    : jobsEndpoints.deleteParticipationMeeting(params?.eventId);
  const { data } = await request.delete<BECandidateMeeting>(url);

  return data;
};
export const createCandidateMeeting = async (
  candidateId: string,
  meetingData: ICreateMeetingData,
  isCandidateMode: boolean
) => {
  const { data } = await request.post(
    isCandidateMode
      ? candidateEndpoints.createCandidateMeeting(candidateId)
      : jobsEndpoints.createCandidateMeeting(candidateId),
    meetingData
  );

  return data;
};

export const searchCandidateMeetings = async (params: {
  candidateId?: string | number;
  text?: string;
}) => {
  const { data } = await request.get<PaginateResponse<BECandidateMeeting>>(
    schedulesSearch.searchMeeting,
    { params }
  );

  return normalizeCandidateMeeting(data);
};

export const getMeetingFilters = async ({
  params,
}: {
  params: { [key: string]: string };
}): Promise<any> => {
  const url = schedulesSearchEndpoints.meetingFilter;
  const { data } = await request.get<any>(url, { params });
  return data;
};
