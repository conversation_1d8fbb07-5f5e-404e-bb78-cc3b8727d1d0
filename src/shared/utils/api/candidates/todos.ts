import {
  candidateEndpoints,
  jobsEndpoints,
  schedulesSearchEndpoints,
} from '../../constants/servicesEndpoints';
import { normalizeCandidateTodo } from '../../normalizers/beforeCacheCandidateInfo';
import request from '../../toolkit/request';
import type {
  BECandidateTodo,
  BatchAddCandidateTodoRequest,
  CandidateTodoRequest,
  CandidateTodoSearchParams,
  ICandidateTodo,
} from '@shared/types/candidates';
import type { PaginateResponse } from '@shared/types/response';
import type { TodoEntityType, TodoStatusType } from '@shared/types/todo';

export const getCandidatesPagedTodos = async (params: {
  candidateId?: string | number;
  projectId?: string | number;
  page?: number;
  size?: number;
  text?: string;
  status?: TodoStatusType;
  onlyDone?: boolean;
  assigneeUserIds?: number[];
  participationId?: string | number;
  entityType?: TodoEntityType;
  isCandidateMode: boolean;
}): Promise<PaginateResponse<ICandidateTodo>> => {
  const { isCandidateMode, ...resetParams } = params;
  const url = isCandidateMode ? candidateEndpoints.todos : jobsEndpoints.todos;

  const { data } = await request.get<PaginateResponse<BECandidateTodo>>(url, {
    params: resetParams,
  });

  return {
    ...data,
    content: data.content.map(normalizeCandidateTodo),
  };
};

export const addCandidateTodo = async (params: {
  id: string;
  isCandidateMode: boolean;
  body: CandidateTodoRequest;
}): Promise<BECandidateTodo> => {
  const { id, isCandidateMode, body } = params;

  const url = isCandidateMode
    ? candidateEndpoints.addTodo(id)
    : jobsEndpoints.addTodoParticipation(id);
  const { data } = await request.post<BECandidateTodo>(url, body);

  return data;
};

export const editCandidateTodo = async (
  todoId: string,
  body: CandidateTodoRequest,
  isCandidateMode: boolean
) => {
  const url = isCandidateMode
    ? candidateEndpoints.editTodo(todoId)
    : jobsEndpoints.editTodoParticipation(todoId);
  return request.put<BECandidateTodo>(url, body);
};

export const removeCandidateTodo = async (params: {
  todoId: string;
  isCandidateMode: boolean;
}): Promise<BECandidateTodo> => {
  const url = params?.isCandidateMode
    ? candidateEndpoints.deleteTodo(params.todoId)
    : jobsEndpoints.deleteTodoParticipation(params.todoId);
  const { data } = await request.delete<BECandidateTodo>(url);

  return data;
};

export const getCandidatesTodosSearch = async (
  params: CandidateTodoSearchParams
): Promise<PaginateResponse<ICandidateTodo>> => {
  const url = schedulesSearchEndpoints.searchTodo;

  const { data } = await request.get<PaginateResponse<BECandidateTodo>>(url, {
    params,
  });

  return {
    ...data,
    content: data.content.map(normalizeCandidateTodo),
  };
};

export const getCandidatesTodosFilters = async (params: {
  params: {
    searchId: string;
  };
}): Promise<any> => {
  const url = schedulesSearchEndpoints.filterTodos;
  const { data } = await request.get<any>(url, {
    params: params.params,
  });

  return data;
};

export const batchAddCandidateTodo = async (params: {
  body: BatchAddCandidateTodoRequest;
}): Promise<BECandidateTodo> => {
  const { data } = await request.post<BECandidateTodo>(
    candidateEndpoints.batchAddTodo,
    params
  );

  return data;
};

export const getTodoFilters = async (params: {
  params: {
    searchId: string;
  };
}): Promise<any> => {
  const { searchId } = params.params;
  const url = schedulesSearchEndpoints.todoFilter;
  const { data } = await request.get<any>(url, { params: { searchId } });
  return data;
};
