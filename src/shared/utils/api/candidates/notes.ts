import {
  candidateEndpoints,
  jobsEndpoints,
} from '@shared/utils/constants/servicesEndpoints';
import schedulesSearch from '@shared/utils/constants/servicesEndpoints/services/schedulesSearch';
import { normalizeCandidateNote } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import sortByKey from '@shared/utils/toolkit/date';
import request from '@shared/utils/toolkit/request';
import type {
  BatchAddCandidateNoteRequest,
  BECandidateNote,
  CandidateNoteRequest,
} from '@shared/types/candidates';
import type { PaginateResponse } from '@shared/types/response';
import type { NoteVisibilityType } from '@shared/utils/constants/enums/candidateDb';

export const getCandidatesNotes = async (params: {
  candidateId?: string | number;
  page?: number;
  size?: number;
  text?: string;
  visibility?: NoteVisibilityType;
  onlyDone?: boolean;
}) => {
  const { data } = await request.get<PaginateResponse<BECandidateNote>>(
    candidateEndpoints.searchNote,
    { params }
  );
  const { content, ...rest } = data;

  return {
    ...rest,
    content: sortByKey(content, 'createdDate').map(normalizeCandidateNote),
  };
};

export const addCandidateNote = async (
  candidateId: number,
  body: CandidateNoteRequest
): Promise<BECandidateNote> => {
  const url = candidateEndpoints.addNoteCandidate(candidateId);
  const { data } = await request.post<BECandidateNote>(url, body);

  return data;
};

export const editCandidateNote = async (
  noteId: string,
  body: CandidateNoteRequest
) => {
  const url = candidateEndpoints.editNoteCandidate(noteId);

  return request.put<BECandidateNote>(url, body);
};

export const removeCandidateNote = async (
  noteId: string
): Promise<BECandidateNote> => {
  const url = candidateEndpoints.deleteNoteCandidate(noteId);
  const { data } = await request.delete<BECandidateNote>(url);

  return data;
};

export const batchAddCandidateNote = async (
  params: BatchAddCandidateNoteRequest
) => {
  const { data } = await request.post(candidateEndpoints.batchAddNote, params);

  return data;
};

export const searchCandidateNotes = async (params: {
  candidateId?: string | number;
  participationId?: string | number;
  visibilities?: NoteVisibilityType[];
  text?: string;
}) => {
  const { data } = await request.get<PaginateResponse<BECandidateNote>>(
    schedulesSearch.searchNote,
    { params }
  );
  const { content, ...rest } = data;

  return {
    ...rest,
    content: sortByKey(content, 'createdDate').map(normalizeCandidateNote),
  };
};

export const addParticipationNote = async (
  participationId: string,
  body: CandidateNoteRequest
): Promise<BECandidateNote> => {
  const url = jobsEndpoints.addNoteParticipation(participationId);
  const { data } = await request.post<BECandidateNote>(url, body);

  return data;
};

export const editParticipationNote = async (
  noteId: string,
  body: CandidateNoteRequest
): Promise<BECandidateNote> => {
  const url = jobsEndpoints.editNoteParticipation(noteId);
  const { data } = await request.put<BECandidateNote>(url, body);

  return data;
};

export const deleteParticipationNote = async (
  noteId: string
): Promise<BECandidateNote> => {
  const url = jobsEndpoints.deleteNoteParticipation(noteId);
  const { data } = await request.delete<BECandidateNote>(url);

  return data;
};
