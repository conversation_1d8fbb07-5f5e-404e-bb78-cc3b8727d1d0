import type { ApplicantType, CandidateType, JobProps } from './jobsProps';
import type { ProjectProps } from './project';
import type { UserType } from './user';
import type { PipelineProps } from '@shared/types/pipelineTypes';

export type ActivityType =
  | 'PIPELINE_CHANGED'
  | 'REVIEW_ADDED'
  | 'REVIEW_MODIFIED'
  | 'REVIEW_REMOVED'
  | 'NOTE_ADDED'
  | 'NOTE_MODIFIED'
  | 'NOTE_REMOVED'
  | 'TODO_ADDED'
  | 'TODO_REMOVED'
  | 'TODO_MODIFIED'
  | 'MEETING_SCHEDULED'
  | 'MEETING_MODIFIED'
  | 'MEETING_CANCELED'
  | 'MEETING_REMOVED'
  | 'USER_APPLIED'
  | 'USER_WITHDRAWN'
  | 'CANDIDATE_SELECTED'
  | 'CANDIDATE_REJECTED'
  | 'JOB_STATUS_CHANGED'
  | 'JOB_PRIORITY_CHANGED'
  | 'JOB_UPDATED'
  | 'JOB_SUBMITTED_AS_CLIENT'
  | 'JOB_WITHDRAWN_AS_CLIENT'
  | 'JOB_ADDED_TO_PROJECT'
  | 'JOB_REMOVED_FROM_PROJECT'
  | 'CANDIDATE_RELINKED'
  | 'CANDIDATE_REMOVED'
  | 'COLLABORATOR_ADDED_TO_PROJECT'
  | 'COLLABORATOR_REMOVED_FROM_PROJECT'
  | 'SCOREBOARD_UPDATED'
  | 'DOCUMENT_UPDATED'
  | 'PROJECT_UPDATED'
  | 'VENDOR_CLIENT_ADDED'
  | 'VENDOR_REMOVED'
  | 'COLLABORATOR_ADDED'
  | 'COLLABORATOR_REMOVED'
  | 'CLIENT_REMOVED'
  | 'CANDIDATE_SUBMITTED'
  | 'CANDIDATE_WITHDRAWN'
  | 'CLIENT_REMOVED';

export interface ActivityProps {
  id: string;
  pageId: string;
  createdDate: string;
  type: ActivityType;
  user: UserType;
  project: ProjectProps | null;
  job: JobProps;
  participationId: string;
  applicantUser?: ApplicantType;
  candidateUser: CandidateType;
  collaborator?: Pick<
    UserType,
    'id' | 'croppedImageUrl' | 'name' | 'surname' | 'username'
  > & {
    occupationName: string;
  };
  jobStatus: string | null;
  lastJobStatus: string | null;
  jobPriority: string | null;
  lastJobPriority: string | null;
  pipeline: PipelineProps;
  lastPipeline: PipelineProps;
  relinked: boolean | null;
  reviewId: string | null;
  reviewText: string | null;
  reviewScore: number | null;
  lastReviewText: string | null;
  lastReviewScore: number | null;
  noteId: string | null;
  noteText: string | null;
  lastNoteText: string | null;
  noteVisibility: string | null;
  lastNoteVisibility: string | null;
  noteFileIds: string[] | null;
  lastNoteFileIds: string[] | null;
  todoId: string | null;
  todoTitle: string | null;
  todoDescription: string | null;
  todoAssigneeUserId: string | null;
  todoStatus: string | null;
  todoAssigneeUser: UserType | null;
  todoStartDateTime: string | null;
  todoEndDateTime: string | null;
  lastTodoTitle: string | null;
  lastTodoDescription: string | null;
  lastTodoAssigneeUser: UserType | null;
  lastTodoStatus: string | null;
  lastTodoStartDateTime: string | null;
  lastTodoEndDateTime: string | null;
  meetingId: string | null;
  meetingTitle: string | null;
  meetingType: string | null;
  meetingFileIds: string[] | null;
  meetingStartDateTime: string | null;
  lastMeetingTitle: string | null;
  lastMeetingType: string | null;
  lastMeetingStartDateTime: string | null;
  vendorPage: any | null;
  clientPage: any | null;
  submittedJobModel: any | null;
  automated?: boolean;
}
