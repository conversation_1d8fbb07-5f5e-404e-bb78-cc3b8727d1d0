'use client';

import React, { createContext, useContext, useState, useMemo } from 'react';
import type { NormalizedMeetingTemplate } from '@shared/hooks/schedules/useMeetingTemplates';
import type { ReactNode } from 'react';

interface DefaultTemplateValueContextType {
  defaultTemplateId: string | null;
}

interface DefaultTemplateActionContextType {
  setDefaultTemplateId: (templateId: string | null) => void;
  getDefaultTemplate: (
    templates: NormalizedMeetingTemplate[]
  ) => NormalizedMeetingTemplate | undefined;
}

const DefaultTemplateValueContext = createContext<
  DefaultTemplateValueContextType | undefined
>(undefined);

const DefaultTemplateActionContext = createContext<
  DefaultTemplateActionContextType | undefined
>(undefined);

interface DefaultTemplateProviderProps {
  children: ReactNode;
}

export const DefaultTemplateProvider: React.FC<
  DefaultTemplateProviderProps
> = ({ children }) => {
  const [defaultTemplateId, setDefaultTemplateIdState] = useState<
    string | null
  >(null);

  const actions = useMemo<DefaultTemplateActionContextType>(
    () => ({
      setDefaultTemplateId: (templateId: string | null) => {
        setDefaultTemplateIdState(templateId);
      },
      getDefaultTemplate: (templates: NormalizedMeetingTemplate[]) => {
        if (!defaultTemplateId) return undefined;

        return templates.find((template) => template.id === defaultTemplateId);
      },
    }),
    [defaultTemplateId]
  );

  const values = useMemo<DefaultTemplateValueContextType>(
    () => ({
      defaultTemplateId,
    }),
    [defaultTemplateId]
  );

  return (
    <DefaultTemplateActionContext.Provider value={actions}>
      <DefaultTemplateValueContext.Provider value={values}>
        {children}
      </DefaultTemplateValueContext.Provider>
    </DefaultTemplateActionContext.Provider>
  );
};

export const useDefaultTemplateValue = (): DefaultTemplateValueContextType => {
  const context = useContext(DefaultTemplateValueContext);
  if (context === undefined) {
    throw new Error(
      'useDefaultTemplateValue must be used within a DefaultTemplateProvider'
    );
  }

  return context;
};

export const useDefaultTemplateActions =
  (): DefaultTemplateActionContextType => {
    const context = useContext(DefaultTemplateActionContext);
    if (context === undefined) {
      throw new Error(
        'useDefaultTemplateActions must be used within a DefaultTemplateProvider'
      );
    }

    return context;
  };

export const useDefaultTemplate = () => {
  const values = useDefaultTemplateValue();
  const actions = useDefaultTemplateActions();

  return {
    ...values,
    ...actions,
  };
};
