{"name": "lobox_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=\"--max_old_space_size=6144\" next dev -p 3005", "build": "NODE_OPTIONS=\"--max_old_space_size=6144\" next build", "start": "next start", "pm2": "pm2 start ecosystem.config.js && pm2-runtime ecosystem.config.js", "start:node": "NODE_OPTIONS=\"--max_old_space_size=10240\" next start", "postinstall": "patch-package", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "ANALYZE=true next build", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@artsy/fresnel": "^1.7.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hello-pangea/dnd": "^17.0.0", "@mui/material": "^6.1.2", "@reach/popover": "^0.13.2", "@react-input/mask": "^2.0.4", "@sentry/nextjs": "^7.98.0", "@storybook/addon-console": "^3.0.0", "@sweepbright/use-click-outside": "^0.3.0", "@tanstack/react-query": "^4.29.25", "@tanstack/react-query-devtools": "^5.17.0", "@tanstack/react-query-next-experimental": "^5.0.0-alpha.80", "@tanstack/react-store": "^0.3.1", "@tanstack/react-virtual": "^3.11.2", "@tanstack/store": "^0.5.5", "@types/react-datepicker": "^6.2.0", "autoprefixer": "^10.4.20", "axios": "^1.6.8", "babel-plugin-react-compiler": "^19.1.0-rc.2", "braintree-web": "^3.117.0", "classnames": "^2.2.5", "date-fns": "^2.26.0", "dayjs": "^1.10.7", "emoji-picker-react": "^4.7.1", "emoji-regex": "^10.2.1", "express": "^4.17.1", "fast-average-color": "^9.1.1", "firebase": "^11.6.0", "formik": "^2.2.6", "framer-motion": "^10.16.16", "fuse.js": "^6.4.6", "glob": "^11.0.3", "helmet": "^4.2.0", "holy-loader": "^2.2.10", "i18next": "^19.8.7", "i18next-browser-languagedetector": "^6.1.2", "icomoon-react": "^3.0.0", "js-cookie": "^2.2.1", "leaflet": "^1.9.4", "libphonenumber-js": "^1.11.4", "localforage": "^1.10.0", "lodash": "^4.17.20", "memoize-one": "^6.0.0", "next": "^15.3.3", "next-with-workbox": "^3.0.5", "normalize-url": "^7.0.3", "postcss": "^8.4.47", "qs": "^6.9.6", "query-string": "^5.0.1", "quill": "1.3.7", "quill-delta": "^4.2.2", "quill-magic-url": "^4.1.3", "quill-mention": "^3.0.3", "react": "^19.1.0", "react-avatar-editor": "^12.0.0", "react-beautiful-dnd": "^13.1.1", "react-circular-progressbar": "^2.0.3", "react-datepicker": "^4.8.0", "react-dom": "^19.1.0", "react-drag-drawer": "^3.3.4", "react-dropzone": "^14.2.3", "react-easy-crop": "^4.6.1", "react-html-parser": "^2.0.2", "react-image-file-resizer": "^0.4.8", "react-infinite-scroller": "^1.2.4", "react-intersection-observer": "^8.33.1", "react-leaflet": "^4.2.1", "react-lottie": "^1.2.4", "react-minimal-pie-chart": "^8.1.0", "react-overlays": "^5.2.1", "react-pdf": "^9.1.1", "react-qr-code": "^2.0.11", "react-quill": "^2.0.0", "react-quill-new": "^3.3.3", "react-render-if-visible": "^2.1.0", "react-responsive-carousel": "^3.2.23", "react-slider": "^2.0.4", "react-tagsinput": "^3.19.0", "react-toastify": "^9.1.3", "react-virtuoso": "^4.6.2", "recharts": "^2.0.9", "reconnecting-websocket": "^4.4.0", "resize-observer-polyfill": "^1.5.1", "sanitize-html": "^2.11.0", "sharp": "^0.33.0", "swiper": "^11.1.4", "tailwindcss": "^3.4.14", "ua-parser-js": "^1.0.2", "use-long-press": "^2.0.2", "uuid": "^8.3.2", "virtua": "^0.41.0", "websocket": "^1.0.34", "winston": "^3.9.0", "workbox-cacheable-response": "^6.5.3", "workbox-core": "^6.5.3", "workbox-expiration": "^6.5.3", "workbox-precaching": "^6.5.3", "workbox-routing": "^6.5.3", "workbox-strategies": "^6.5.3", "workbox-window": "^6.5.3", "yup": "^0.32.9"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.3", "@next/bundle-analyzer": "^15.4.5", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@testing-library/cypress": "^10.0.2", "@types/braintree-web": "^3", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.8", "@types/node": "^20", "@types/query-string": "5", "@types/react": "^19.1.0", "@types/react-avatar-editor": "^13.0.4", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.0", "@types/react-html-parser": "^2.0.6", "@types/react-lottie": "^1.2.10", "@types/react-slider": "^1.3.6", "@types/react-tagsinput": "^3.20.6", "@types/sanitize-html": "^2.9.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "cypress": "^13.17.0", "eslint": "^8.48.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-storybook": "^0.11.1", "eslint-plugin-unused-imports": "^4.1.4", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.3.3", "sass": "1.69.5", "storybook": "^8.4.7", "storybook-dark-mode": "^4.0.2", "typescript": "^5"}, "packageManager": "pnpm@9.12.2"}